<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class FileRelation extends BaseCituiModel
{
    protected $table = 'file_relations';
    protected $primaryKey = 'relation_id';
    
    protected $fillable = [
        'business_type',
        'business_id',
        'file_type',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'file_hash',
        'sort_order'
    ];
    
    protected $casts = [
        'business_id' => 'integer',
        'file_size' => 'integer',
        'sort_order' => 'integer'
    ];
    
    protected $appends = [
        'file_full_url',
        'formatted_file_size'
    ];
    
    /**
     * 多态关联 - 业务对象
     */
    public function business()
    {
        return $this->morphTo('business', 'business_type', 'business_id');
    }
    
    /**
     * 获取完整文件URL
     */
    public function getFileFullUrlAttribute(): string
    {
        if (!$this->file_path) {
            return '';
        }
        
        if (str_starts_with($this->file_path, 'http')) {
            return $this->file_path;
        }
        
        return config('app.url') . $this->file_path;
    }
    
    /**
     * 获取格式化的文件大小
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return '未知';
        }
        
        $size = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * 查询作用域 - 按业务类型筛选
     */
    public function scopeByBusinessType(Builder $query, string $businessType): Builder
    {
        return $query->where('business_type', $businessType);
    }
    
    /**
     * 查询作用域 - 按业务ID筛选
     */
    public function scopeByBusinessId(Builder $query, int $businessId): Builder
    {
        return $query->where('business_id', $businessId);
    }
    
    /**
     * 查询作用域 - 按文件类型筛选
     */
    public function scopeByFileType(Builder $query, string $fileType): Builder
    {
        return $query->where('file_type', $fileType);
    }
    
    /**
     * 查询作用域 - 图片文件
     */
    public function scopeImages(Builder $query): Builder
    {
        return $query->where('file_type', 'image');
    }
    
    /**
     * 查询作用域 - 视频文件
     */
    public function scopeVideos(Builder $query): Builder
    {
        return $query->where('file_type', 'video');
    }
    
    /**
     * 查询作用域 - 文档文件
     */
    public function scopeDocuments(Builder $query): Builder
    {
        return $query->where('file_type', 'document');
    }
    
    /**
     * 查询作用域 - 按排序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }
    
    /**
     * 检查是否为图片
     */
    public function isImage(): bool
    {
        return $this->file_type === 'image' || 
               str_starts_with($this->mime_type, 'image/');
    }
    
    /**
     * 检查是否为视频
     */
    public function isVideo(): bool
    {
        return $this->file_type === 'video' || 
               str_starts_with($this->mime_type, 'video/');
    }
    
    /**
     * 检查是否为文档
     */
    public function isDocument(): bool
    {
        return $this->file_type === 'document' || 
               in_array($this->mime_type, [
                   'application/pdf',
                   'application/msword',
                   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                   'application/vnd.ms-excel',
                   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
               ]);
    }
    
    /**
     * 获取文件扩展名
     */
    public function getFileExtension(): string
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }
    
    /**
     * 获取文件类型文本
     */
    public function getFileTypeTextAttribute(): string
    {
        $types = [
            'image' => '图片',
            'video' => '视频',
            'document' => '文档',
            'audio' => '音频',
            'archive' => '压缩包',
            'other' => '其他'
        ];
        
        return $types[$this->file_type] ?? '未知';
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'relation_id' => $this->relation_id,
            'business_type' => $this->business_type,
            'business_id' => $this->business_id,
            'file_type' => $this->file_type,
            'file_type_text' => $this->file_type_text,
            'file_name' => $this->file_name,
            'file_path' => $this->file_path,
            'file_full_url' => $this->file_full_url,
            'file_size' => $this->file_size,
            'formatted_file_size' => $this->formatted_file_size,
            'mime_type' => $this->mime_type,
            'file_hash' => $this->file_hash,
            'sort_order' => $this->sort_order,
            'file_extension' => $this->getFileExtension(),
            'is_image' => $this->isImage(),
            'is_video' => $this->isVideo(),
            'is_document' => $this->isDocument(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}