<?php
/**
 * PointService 使用示例
 * 
 * 这个文件展示了如何使用 PointService 进行积分管理
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\Citui\PointService;

// 创建 PointService 实例
$pointService = new PointService();

echo "=== CitUI 积分服务使用示例 ===\n\n";

// 示例1: 获取积分规则
echo "1. 获取积分规则列表:\n";
echo "   \$pointService->getPointRules();\n";
echo "   // 返回所有可用的积分规则\n\n";

// 示例2: 获取奖励配置
echo "2. 获取奖励配置列表:\n";
echo "   \$pointService->getRewardConfigs(['is_available' => true]);\n";
echo "   // 返回所有可用的奖励配置\n\n";

// 示例3: 发放积分
echo "3. 自动发放积分:\n";
echo "   \$result = \$pointService->awardPoints(\n";
echo "       \$userId = 1,\n";
echo "       \$ruleCode = 'register_bonus',\n";
echo "       \$context = [\n";
echo "           'source_type' => 'register',\n";
echo "           'description' => '用户注册奖励'\n";
echo "       ]\n";
echo "   );\n";
echo "   // 根据规则自动发放积分\n\n";

// 示例4: 兑换奖励
echo "4. 兑换奖励:\n";
echo "   \$result = \$pointService->exchangeReward(\n";
echo "       \$userId = 1,\n";
echo "       \$configId = 1\n";
echo "   );\n";
echo "   // 使用积分兑换奖励\n\n";

// 示例5: 获取用户积分记录
echo "5. 获取用户积分记录:\n";
echo "   \$records = \$pointService->getPointRecords(\n";
echo "       \$userId = 1,\n";
echo "       \$filters = [\n";
echo "           'record_type' => 'earn',\n";
echo "           'start_date' => '2024-01-01',\n";
echo "           'end_date' => '2024-12-31'\n";
echo "       ]\n";
echo "   );\n";
echo "   // 获取用户的积分变动记录\n\n";

// 示例6: 获取用户积分统计
echo "6. 获取用户积分统计:\n";
echo "   \$stats = \$pointService->getUserPointsStatistics(\n";
echo "       \$userId = 1,\n";
echo "       \$days = 30\n";
echo "   );\n";
echo "   // 获取用户最近30天的积分统计\n\n";

// 示例7: 扣除积分
echo "7. 扣除用户积分:\n";
echo "   \$result = \$pointService->deductPoints(\n";
echo "       \$userId = 1,\n";
echo "       \$points = 100,\n";
echo "       \$reason = '违规扣分'\n";
echo "   );\n";
echo "   // 扣除用户积分\n\n";

// 示例8: 获取积分排行榜
echo "8. 获取积分排行榜:\n";
echo "   \$leaderboard = \$pointService->getPointsLeaderboard(\n";
echo "       \$limit = 50,\n";
echo "       \$period = 'month'\n";
echo "   );\n";
echo "   // 获取本月积分排行榜\n\n";

// 示例9: 处理过期积分
echo "9. 处理过期积分:\n";
echo "   \$result = \$pointService->processExpiredPoints();\n";
echo "   // 批量处理过期的积分记录\n\n";

echo "=== 主要功能特性 ===\n";
echo "✓ 积分规则管理和自动发放\n";
echo "✓ 积分记录查询和统计\n";
echo "✓ 奖励兑换和库存管理\n";
echo "✓ 积分排行榜\n";
echo "✓ 过期积分处理\n";
echo "✓ 缓存优化\n";
echo "✓ 事务安全\n";
echo "✓ 日志记录\n";
echo "✓ 异常处理\n\n";

echo "=== 使用注意事项 ===\n";
echo "1. 所有涉及积分变动的操作都使用数据库事务确保数据一致性\n";
echo "2. 积分发放会检查每日限制和总限制\n";
echo "3. 奖励兑换会检查用户积分余额和奖励库存\n";
echo "4. 统计数据使用缓存提高性能\n";
echo "5. 所有操作都有详细的日志记录\n\n";

echo "示例完成！\n";