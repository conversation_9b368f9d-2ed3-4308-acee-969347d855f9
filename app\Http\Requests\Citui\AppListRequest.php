<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class AppListRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'category_id' => 'nullable|integer|exists:ct_app_categories,category_id',
            'developer' => 'nullable|string|max:100',
            'min_rating' => 'nullable|numeric|between:0,5',
            'max_rating' => 'nullable|numeric|between:0,5|gte:min_rating',
            'min_size' => 'nullable|integer|min:0',
            'max_size' => 'nullable|integer|min:0|gte:min_size',
            'is_featured' => 'nullable|boolean',
            'is_popular' => 'nullable|boolean',
            'include_subcategories' => 'nullable|boolean',
            'sort_by' => 'nullable|string|in:rating,downloads,views,name,size,created_at,updated_at',
            'sort_order' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'category_id' => '分类ID',
            'developer' => '开发商',
            'min_rating' => '最低评分',
            'max_rating' => '最高评分',
            'min_size' => '最小大小',
            'max_size' => '最大大小',
            'is_featured' => '是否推荐',
            'is_popular' => '是否热门',
            'include_subcategories' => '包含子分类',
            'sort_by' => '排序字段',
            'sort_order' => '排序方向',
            'page' => '页码',
            'per_page' => '每页数量'
        ]);
    }
}