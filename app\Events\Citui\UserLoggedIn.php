<?php

declare(strict_types=1);

namespace App\Events\Citui;

use App\Models\Citui\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserLoggedIn
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public string $loginType;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, string $loginType = 'password')
    {
        $this->user = $user;
        $this->loginType = $loginType;
    }
}