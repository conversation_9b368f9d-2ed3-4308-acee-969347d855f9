<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class UserLogin extends BaseCituiModel
{
    protected $table = 'user_logins';
    protected $primaryKey = 'login_id';
    
    protected $fillable = [
        'user_id',
        'login_type',
        'login_ip',
        'login_device',
        'user_agent',
        'login_status',
        'failure_reason',
        'session_id',
        'login_duration',
        'logout_at'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'login_duration' => 'integer',
        'logout_at' => 'datetime'
    ];
    
    // 不使用updated_at字段
    const UPDATED_AT = null;
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 查询作用域 - 成功登录
     */
    public function scopeSuccessful(Builder $query): Builder
    {
        return $query->where('login_status', 'success');
    }
    
    /**
     * 查询作用域 - 失败登录
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('login_status', 'failed');
    }
    
    /**
     * 查询作用域 - 按登录类型筛选
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('login_type', $type);
    }
    
    /**
     * 查询作用域 - 按IP筛选
     */
    public function scopeByIp(Builder $query, string $ip): Builder
    {
        return $query->where('login_ip', $ip);
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 获取登录设备信息
     */
    public function getDeviceInfoAttribute(): array
    {
        if (!$this->user_agent) {
            return [];
        }
        
        // 简单的设备信息解析
        $userAgent = $this->user_agent;
        $device = [];
        
        if (strpos($userAgent, 'Mobile') !== false) {
            $device['type'] = 'mobile';
        } elseif (strpos($userAgent, 'Tablet') !== false) {
            $device['type'] = 'tablet';
        } else {
            $device['type'] = 'desktop';
        }
        
        // 操作系统检测
        if (strpos($userAgent, 'Windows') !== false) {
            $device['os'] = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $device['os'] = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $device['os'] = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $device['os'] = 'Android';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            $device['os'] = 'iOS';
        }
        
        // 浏览器检测
        if (strpos($userAgent, 'Chrome') !== false) {
            $device['browser'] = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $device['browser'] = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $device['browser'] = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $device['browser'] = 'Edge';
        }
        
        return $device;
    }
    
    /**
     * 检查是否为成功登录
     */
    public function isSuccessful(): bool
    {
        return $this->login_status === 'success';
    }
    
    /**
     * 检查是否为失败登录
     */
    public function isFailed(): bool
    {
        return $this->login_status === 'failed';
    }
    
    /**
     * 设置登出时间
     */
    public function setLogout(): bool
    {
        if ($this->login_duration === null && $this->logout_at === null) {
            $this->logout_at = now();
            $this->login_duration = $this->created_at->diffInSeconds($this->logout_at);
            return $this->save();
        }
        
        return false;
    }
    
    /**
     * 获取登录时长（格式化）
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->login_duration) {
            return '未知';
        }
        
        $hours = floor($this->login_duration / 3600);
        $minutes = floor(($this->login_duration % 3600) / 60);
        $seconds = $this->login_duration % 60;
        
        if ($hours > 0) {
            return sprintf('%d小时%d分钟', $hours, $minutes);
        } elseif ($minutes > 0) {
            return sprintf('%d分钟%d秒', $minutes, $seconds);
        } else {
            return sprintf('%d秒', $seconds);
        }
    }
}