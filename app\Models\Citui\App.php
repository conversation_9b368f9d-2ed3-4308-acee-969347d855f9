<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class App extends BaseCituiModel
{
    protected $table = 'apps';
    protected $primaryKey = 'app_id';
    
    protected $fillable = [
        'category_id',
        'app_name',
        'app_package',
        'app_version',
        'developer',
        'app_size',
        'download_url',
        'logo_url',
        'description',
        'features',
        'screenshots',
        'rating',
        'rating_count',
        'download_count',
        'view_count',
        'status',
        'is_featured'
    ];
    
    protected $casts = [
        'category_id' => 'integer',
        'features' => 'array',
        'screenshots' => 'array',
        'rating' => 'decimal:2',
        'rating_count' => 'integer',
        'download_count' => 'integer',
        'view_count' => 'integer',
        'app_size' => 'integer',
        'is_featured' => 'boolean'
    ];
    
    protected $appends = [
        'logo_full_url',
        'formatted_size',
        'formatted_downloads',
        'star_rating',
        'status_text'
    ];
    
    /**
     * 关联APP分类
     */
    public function category()
    {
        return $this->belongsTo(AppCategory::class, 'category_id', 'category_id');
    }
    
    /**
     * 关联评测报告
     */
    public function evaluationReports()
    {
        return $this->hasMany(EvaluationReport::class, 'app_id', 'app_id');
    }
    
    /**
     * 关联放水线索
     */
    public function waterClues()
    {
        return $this->hasMany(WaterClue::class, 'app_id', 'app_id');
    }
    
    /**
     * 关联文件（Logo等）
     */
    public function files()
    {
        return $this->morphMany(FileRelation::class, 'business', 'business_type', 'business_id')
                    ->where('business_type', 'app');
    }
    
    /**
     * 查询作用域 - 推荐APP
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }
    
    /**
     * 查询作用域 - 按评分排序
     */
    public function scopeByRating(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('rating', $direction);
    }
    
    /**
     * 查询作用域 - 按下载量排序
     */
    public function scopeByDownloads(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('download_count', $direction);
    }
    
    /**
     * 查询作用域 - 按查看量排序
     */
    public function scopeByViews(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('view_count', $direction);
    }
    
    /**
     * 查询作用域 - 按分类筛选
     */
    public function scopeByCategory(Builder $query, int $categoryId): Builder
    {
        return $query->where('category_id', $categoryId);
    }
    
    /**
     * 查询作用域 - 按开发商筛选
     */
    public function scopeByDeveloper(Builder $query, string $developer): Builder
    {
        return $query->where('developer', 'like', "%{$developer}%");
    }
    
    /**
     * 查询作用域 - 搜索APP
     */
    public function scopeSearch(Builder $query, string $keyword): Builder
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('app_name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%")
              ->orWhere('developer', 'like', "%{$keyword}%");
        });
    }
    
    /**
     * 查询作用域 - 按评分范围筛选
     */
    public function scopeByRatingRange(Builder $query, float $minRating = 0, ?float $maxRating = null): Builder
    {
        $query->where('rating', '>=', $minRating);
        
        if ($maxRating !== null) {
            $query->where('rating', '<=', $maxRating);
        }
        
        return $query;
    }
    
    /**
     * 查询作用域 - 按大小范围筛选
     */
    public function scopeBySizeRange(Builder $query, int $minSize = 0, ?int $maxSize = null): Builder
    {
        $query->where('app_size', '>=', $minSize);
        
        if ($maxSize !== null) {
            $query->where('app_size', '<=', $maxSize);
        }
        
        return $query;
    }
    
    /**
     * 获取格式化的APP大小
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->app_size) {
            return '未知';
        }
        
        $size = $this->app_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * 获取评分星级
     */
    public function getStarRatingAttribute(): array
    {
        $rating = $this->rating;
        $fullStars = floor($rating);
        $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
        $emptyStars = 5 - $fullStars - $halfStar;
        
        return [
            'full' => $fullStars,
            'half' => $halfStar,
            'empty' => $emptyStars,
            'rating' => $rating
        ];
    }
    
    /**
     * 获取下载量格式化显示
     */
    public function getFormattedDownloadsAttribute(): string
    {
        $count = $this->download_count;
        
        if ($count >= 100000000) {
            return round($count / 100000000, 1) . '亿';
        } elseif ($count >= 10000) {
            return round($count / 10000, 1) . '万';
        } elseif ($count >= 1000) {
            return round($count / 1000, 1) . 'k';
        }
        
        return (string) $count;
    }
    
    /**
     * 检查是否为推荐APP
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }
    
    /**
     * 检查是否活跃
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
    
    /**
     * 获取完整Logo URL
     */
    public function getLogoFullUrlAttribute(): string
    {
        if (!$this->logo_url) {
            return config('app.url') . '/images/default-app-logo.png';
        }
        
        if (str_starts_with($this->logo_url, 'http')) {
            return $this->logo_url;
        }
        
        return config('app.url') . $this->logo_url;
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'active' => '正常',
            'inactive' => '下架',
            'pending' => '待审核'
        ];
        
        return $statuses[$this->status] ?? '未知';
    }
    
    /**
     * 增加查看次数
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }
    
    /**
     * 增加下载次数
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }
    
    /**
     * 更新评分
     */
    public function updateRating(float $newRating): bool
    {
        $currentTotal = $this->rating * $this->rating_count;
        $newTotal = $currentTotal + $newRating;
        $newCount = $this->rating_count + 1;
        $newAverage = $newTotal / $newCount;
        
        $this->rating = round($newAverage, 2);
        $this->rating_count = $newCount;
        
        return $this->save();
    }
    
    /**
     * 获取APP统计数据
     */
    public function getAppStatistics(): array
    {
        return [
            'evaluation_count' => $this->evaluationReports()->count(),
            'clue_count' => $this->waterClues()->count(),
            'rating' => $this->rating,
            'rating_count' => $this->rating_count,
            'download_count' => $this->download_count,
            'view_count' => $this->view_count,
            'is_featured' => $this->is_featured,
            'category_name' => $this->category->category_name ?? '未分类'
        ];
    }
    
    /**
     * 获取相关APP推荐
     */
    public function getRelatedApps(int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('category_id', $this->category_id)
                    ->where('app_id', '!=', $this->app_id)
                    ->active()
                    ->byRating()
                    ->limit($limit)
                    ->get();
    }
    
    /**
     * 检查是否为热门APP
     */
    public function isPopular(): bool
    {
        return $this->rating >= 4.0 && $this->download_count >= 10000;
    }
    
    /**
     * 触发统计更新（异步）
     */
    public function triggerStatisticsUpdate(string $type = 'rating'): void
    {
        try {
            \App\Jobs\UpdateAppStatisticsJob::dispatch($this->app_id, $type);
        } catch (\Exception $e) {
            // 队列任务分发失败，记录日志但不影响主流程
            \Log::warning('APP统计更新任务分发失败', [
                'app_id' => $this->app_id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取统计趋势数据
     */
    public function getStatisticsTrend(int $days = 30): array
    {
        // 这里可以从详细的统计日志表中获取趋势数据
        // 暂时返回基础数据
        return [
            'period' => $days,
            'current_views' => $this->view_count,
            'current_downloads' => $this->download_count,
            'current_rating' => $this->rating,
            'trend_data' => [] // 实际应该包含每日的统计数据
        ];
    }

    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'app_id' => $this->app_id,
            'category_id' => $this->category_id,
            'category' => $this->category?->toApiArray(),
            'app_name' => $this->app_name,
            'app_package' => $this->app_package,
            'app_version' => $this->app_version,
            'developer' => $this->developer,
            'app_size' => $this->app_size,
            'formatted_size' => $this->formatted_size,
            'download_url' => $this->download_url,
            'logo_url' => $this->logo_full_url,
            'description' => $this->description,
            'features' => $this->features,
            'screenshots' => $this->screenshots,
            'rating' => $this->rating,
            'rating_count' => $this->rating_count,
            'star_rating' => $this->star_rating,
            'download_count' => $this->download_count,
            'formatted_downloads' => $this->formatted_downloads,
            'view_count' => $this->view_count,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'is_featured' => $this->is_featured,
            'is_popular' => $this->isPopular(),
            'statistics' => $this->getAppStatistics(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}