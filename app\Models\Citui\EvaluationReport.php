<?php

declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class EvaluationReport extends BaseCituiModel
{
    protected $table = 'evaluation_reports';
    protected $primaryKey = 'report_id';

    protected $fillable = [
        'app_id',
        'user_id',
        'report_title',
        'report_content',
        'task_description',
        'completion_time',
        'difficulty_level',
        'rating',
        'pros',
        'cons',
        'suggestions',
        'screenshots',
        'reward_points',
        'status',
        'view_count',
        'like_count',
        'is_featured',
        'submitted_at',
        'approved_at'
    ];

    protected $casts = [
        'app_id' => 'integer',
        'user_id' => 'integer',
        'completion_time' => 'integer',
        'rating' => 'integer',
        'screenshots' => 'array',
        'reward_points' => 'integer',
        'view_count' => 'integer',
        'like_count' => 'integer',
        'is_featured' => 'boolean',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime'
    ];

    protected $appends = [
        'screenshots_full_urls',
        'status_text',
        'difficulty_text',
        'completion_time_text',
        'rating_stars'
    ];

    /**
     * 关联APP
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'app_id', 'app_id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    /**
     * 关联评测详情
     */
    public function details()
    {
        return $this->hasMany(EvaluationDetail::class, 'report_id', 'report_id');
    }

    /**
     * 关联点赞记录
     */
    public function likes()
    {
        return $this->hasMany(EvaluationLike::class, 'report_id', 'report_id');
    }

    /**
     * 关联评论
     */
    public function comments()
    {
        return $this->hasMany(EvaluationComment::class, 'report_id', 'report_id');
    }

    /**
     * 查询作用域 - 已提交
     */
    public function scopeSubmitted(Builder $query): Builder
    {
        return $query->where('status', 'submitted');
    }

    /**
     * 查询作用域 - 已审核通过
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }

    /**
     * 查询作用域 - 推荐报告
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * 查询作用域 - 按评分筛选
     */
    public function scopeByRating(Builder $query, int $rating): Builder
    {
        return $query->where('rating', $rating);
    }

    /**
     * 查询作用域 - 按难度筛选
     */
    public function scopeByDifficulty(Builder $query, string $difficulty): Builder
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * 检查是否已审核通过
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * 检查是否为推荐报告
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }

    /**
     * 获取完整截图URLs
     */
    public function getScreenshotsFullUrlsAttribute(): array
    {
        if (!$this->screenshots || !is_array($this->screenshots)) {
            return [];
        }

        return array_map(function ($screenshot) {
            if (str_starts_with($screenshot, 'http')) {
                return $screenshot;
            }
            return config('app.url') . $screenshot;
        }, $this->screenshots);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'draft' => '草稿',
            'submitted' => '已提交',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ];

        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取难度文本
     */
    public function getDifficultyTextAttribute(): string
    {
        $difficulties = [
            'easy' => '简单',
            'medium' => '中等',
            'hard' => '困难',
            'expert' => '专家'
        ];

        return $difficulties[$this->difficulty_level] ?? '未知';
    }

    /**
     * 获取完成时间文本
     */
    public function getCompletionTimeTextAttribute(): string
    {
        if (!$this->completion_time) {
            return '未知';
        }

        $hours = intval($this->completion_time / 60);
        $minutes = $this->completion_time % 60;

        if ($hours > 0) {
            return "{$hours}小时{$minutes}分钟";
        } else {
            return "{$minutes}分钟";
        }
    }

    /**
     * 获取评分星级
     */
    public function getRatingStarsAttribute(): array
    {
        $rating = $this->rating;
        $fullStars = $rating;
        $emptyStars = 5 - $rating;

        return [
            'full' => $fullStars,
            'empty' => $emptyStars,
            'rating' => $rating
        ];
    }

    /**
     * 增加查看次数
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * 增加点赞次数
     */
    public function incrementLikeCount(): void
    {
        $this->increment('like_count');
    }

    /**
     * 减少点赞次数
     */
    public function decrementLikeCount(): void
    {
        $this->decrement('like_count');
    }

    /**
     * 检查用户是否已点赞
     */
    public function isLikedByUser(int $userId): bool
    {
        return $this->likes()->where('user_id', $userId)->exists();
    }

    /**
     * 查询作用域 - 按APP筛选
     */
    public function scopeByApp(Builder $query, int $appId): Builder
    {
        return $query->where('app_id', $appId);
    }

    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 查询作用域 - 按点赞数排序
     */
    public function scopeByLikes(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('like_count', $direction);
    }

    /**
     * 查询作用域 - 按查看数排序
     */
    public function scopeByViews(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('view_count', $direction);
    }

    /**
     * 查询作用域 - 热门报告
     */
    public function scopePopular(Builder $query): Builder
    {
        return $query->selectRaw('*, (like_count * 0.6 + view_count * 0.4) as popularity_score')
            ->orderBy('popularity_score', 'desc');
    }

    /**
     * 审核通过
     */
    public function approve(): bool
    {
        $this->status = 'approved';
        $this->approved_at = now();
        return $this->save();
    }

    /**
     * 审核拒绝
     */
    public function reject(): bool
    {
        $this->status = 'rejected';
        return $this->save();
    }

    /**
     * 设为推荐
     */
    public function setFeatured(bool $featured = true): bool
    {
        $this->is_featured = $featured;
        return $this->save();
    }
    
    /**
     * 更新审核状态
     */
    public function updateAuditStatus(string $status, ?string $reason = null): bool
    {
        $statusMap = [
            'published' => 'approved',
            'rejected' => 'rejected'
        ];
        
        $this->status = $statusMap[$status] ?? $status;
        
        if ($status === 'published') {
            $this->approved_at = now();
        } elseif ($status === 'rejected' && $reason) {
            $this->reject_reason = $reason;
        }
        
        return $this->save();
    }

    /**
     * 获取报告统计数据
     */
    public function getReportStatistics(): array
    {
        return [
            'view_count' => $this->view_count,
            'like_count' => $this->like_count,
            'comment_count' => $this->comments()->count(),
            'detail_count' => $this->details()->count(),
            'rating' => $this->rating,
            'completion_time' => $this->completion_time,
            'reward_points' => $this->reward_points
        ];
    }

    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'report_id' => $this->report_id,
            'app_id' => $this->app_id,
            'app' => $this->app?->toApiArray(),
            'user_id' => $this->user_id,
            'user' => $this->user?->toApiArray(),
            'report_title' => $this->report_title,
            'report_content' => $this->report_content,
            'task_description' => $this->task_description,
            'completion_time' => $this->completion_time,
            'completion_time_text' => $this->completion_time_text,
            'difficulty_level' => $this->difficulty_level,
            'difficulty_text' => $this->difficulty_text,
            'rating' => $this->rating,
            'rating_stars' => $this->rating_stars,
            'pros' => $this->pros,
            'cons' => $this->cons,
            'suggestions' => $this->suggestions,
            'screenshots' => $this->screenshots_full_urls,
            'reward_points' => $this->reward_points,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'view_count' => $this->view_count,
            'like_count' => $this->like_count,
            'is_featured' => $this->is_featured,
            'statistics' => $this->getReportStatistics(),
            'submitted_at' => $this->submitted_at?->format('Y-m-d H:i:s'),
            'approved_at' => $this->approved_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}
