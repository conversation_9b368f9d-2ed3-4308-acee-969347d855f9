<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class PointExchange extends BaseCituiModel
{
    protected $table = 'point_exchanges';
    protected $primaryKey = 'exchange_id';
    
    protected $fillable = [
        'user_id',
        'config_id',
        'points_used',
        'quantity',
        'contact_info',
        'status',
        'tracking_info',
        'notes',
        'processed_by',
        'processed_at',
        'completed_at'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'config_id' => 'integer',
        'points_used' => 'integer',
        'quantity' => 'integer',
        'contact_info' => 'array',
        'tracking_info' => 'array',
        'processed_by' => 'integer',
        'processed_at' => 'datetime',
        'completed_at' => 'datetime'
    ];
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联奖励配置
     */
    public function config()
    {
        return $this->belongsTo(RewardConfig::class, 'config_id', 'config_id');
    }
    
    /**
     * 关联处理人
     */
    public function processor()
    {
        return $this->belongsTo(AdminUser::class, 'processed_by', 'admin_id');
    }
    
    /**
     * 查询作用域 - 待处理
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * 查询作用域 - 处理中
     */
    public function scopeProcessing(Builder $query): Builder
    {
        return $query->where('status', 'processing');
    }
    
    /**
     * 查询作用域 - 已完成
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }
    
    /**
     * 查询作用域 - 已取消
     */
    public function scopeCancelled(Builder $query): Builder
    {
        return $query->where('status', 'cancelled');
    }
    
    /**
     * 检查是否待处理
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
    
    /**
     * 检查是否处理中
     */
    public function isProcessing(): bool
    {
        return $this->status === 'processing';
    }
    
    /**
     * 检查是否已完成
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }
    
    /**
     * 开始处理
     */
    public function startProcessing(int $processedBy): bool
    {
        if (!$this->isPending()) {
            return false;
        }
        
        $this->status = 'processing';
        $this->processed_by = $processedBy;
        $this->processed_at = now();
        
        return $this->save();
    }
    
    /**
     * 完成兑换
     */
    public function complete(): bool
    {
        if (!$this->isProcessing()) {
            return false;
        }
        
        $this->status = 'completed';
        $this->completed_at = now();
        
        return $this->save();
    }
    
    /**
     * 取消兑换
     */
    public function cancel(): bool
    {
        if ($this->isCompleted()) {
            return false;
        }
        
        $this->status = 'cancelled';
        
        return $this->save();
    }
}