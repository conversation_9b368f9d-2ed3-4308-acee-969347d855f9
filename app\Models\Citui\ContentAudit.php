<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class ContentAudit extends BaseCituiModel
{
    protected $table = 'content_audits';
    protected $primaryKey = 'audit_id';
    
    protected $fillable = [
        'rule_id',
        'content_type',
        'content_id',
        'submitter_id',
        'audit_status',
        'auto_audit_score',
        'auto_audit_result',
        'auto_audit_reason',
        'manual_audit_required',
        'auditor_id',
        'audit_result',
        'audit_reason',
        'audit_score',
        'reject_category',
        'content_snapshot',
        'priority_level',
        'submitted_at',
        'audit_started_at',
        'audit_completed_at',
        'timeout_at'
    ];
    
    protected $casts = [
        'rule_id' => 'integer',
        'content_id' => 'integer',
        'submitter_id' => 'integer',
        'auto_audit_score' => 'integer',
        'manual_audit_required' => 'boolean',
        'auditor_id' => 'integer',
        'audit_score' => 'integer',
        'content_snapshot' => 'array',
        'submitted_at' => 'datetime',
        'audit_started_at' => 'datetime',
        'audit_completed_at' => 'datetime',
        'timeout_at' => 'datetime'
    ];
    
    /**
     * 关联审核规则
     */
    public function rule()
    {
        return $this->belongsTo(AuditRule::class, 'rule_id', 'rule_id');
    }
    
    /**
     * 关联提交者
     */
    public function submitter()
    {
        return $this->belongsTo(User::class, 'submitter_id', 'user_id');
    }
    
    /**
     * 关联审核员
     */
    public function auditor()
    {
        return $this->belongsTo(AdminUser::class, 'auditor_id', 'admin_id');
    }
    
    /**
     * 关联审核日志
     */
    public function logs()
    {
        return $this->hasMany(AuditLog::class, 'audit_id', 'audit_id');
    }
    
    /**
     * 查询作用域 - 待审核
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('audit_status', 'pending');
    }
    
    /**
     * 查询作用域 - 审核中
     */
    public function scopeReviewing(Builder $query): Builder
    {
        return $query->where('audit_status', 'reviewing');
    }
    
    /**
     * 查询作用域 - 已通过
     */
    public function scopePassed(Builder $query): Builder
    {
        return $query->where('audit_status', 'passed');
    }
    
    /**
     * 查询作用域 - 已拒绝
     */
    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('audit_status', 'rejected');
    }
    
    /**
     * 查询作用域 - 已超时
     */
    public function scopeTimeout(Builder $query): Builder
    {
        return $query->where('audit_status', 'timeout');
    }
    
    /**
     * 检查是否待审核
     */
    public function isPending(): bool
    {
        return $this->audit_status === 'pending';
    }
    
    /**
     * 检查是否已通过
     */
    public function isPassed(): bool
    {
        return $this->audit_status === 'passed';
    }
    
    /**
     * 检查是否已拒绝
     */
    public function isRejected(): bool
    {
        return $this->audit_status === 'rejected';
    }
    
    /**
     * 开始审核
     */
    public function startAudit(int $auditorId): bool
    {
        if (!$this->isPending()) {
            return false;
        }
        
        $this->audit_status = 'reviewing';
        $this->auditor_id = $auditorId;
        $this->audit_started_at = now();
        
        return $this->save();
    }
    
    /**
     * 通过审核
     */
    public function pass(string $reason = '', ?int $score = null): bool
    {
        $this->audit_status = 'passed';
        $this->audit_result = 'pass';
        $this->audit_reason = $reason;
        $this->audit_score = $score;
        $this->audit_completed_at = now();
        
        return $this->save();
    }
    
    /**
     * 拒绝审核
     */
    public function reject(string $reason = '', ?string $category = null, ?int $score = null): bool
    {
        $this->audit_status = 'rejected';
        $this->audit_result = 'reject';
        $this->audit_reason = $reason;
        $this->reject_category = $category;
        $this->audit_score = $score;
        $this->audit_completed_at = now();
        
        return $this->save();
    }
}