<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class RewardConfig extends BaseCituiModel
{
    protected $table = 'reward_configs';
    protected $primaryKey = 'config_id';
    
    protected $fillable = [
        'reward_name',
        'reward_type',
        'reward_value',
        'required_points',
        'stock_quantity',
        'used_quantity',
        'reward_image',
        'description',
        'terms_conditions',
        'is_active',
        'sort_order',
        'valid_from',
        'valid_until'
    ];
    
    protected $casts = [
        'reward_value' => 'decimal:2',
        'required_points' => 'integer',
        'stock_quantity' => 'integer',
        'used_quantity' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime'
    ];
    
    protected $appends = [
        'reward_image_full_url',
        'remaining_stock',
        'is_available',
        'reward_type_text'
    ];
    
    /**
     * 关联积分记录
     */
    public function pointRecords()
    {
        return $this->hasMany(PointRecord::class, 'reward_config_id', 'config_id');
    }
    
    /**
     * 获取完整奖励图片URL
     */
    public function getRewardImageFullUrlAttribute(): string
    {
        if (!$this->reward_image) {
            return config('app.url') . '/images/default-reward.png';
        }
        
        if (str_starts_with($this->reward_image, 'http')) {
            return $this->reward_image;
        }
        
        return config('app.url') . $this->reward_image;
    }
    
    /**
     * 获取剩余库存
     */
    public function getRemainingStockAttribute(): int
    {
        return max(0, $this->stock_quantity - $this->used_quantity);
    }
    
    /**
     * 检查奖励是否可用
     */
    public function getIsAvailableAttribute(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        
        if ($this->remaining_stock <= 0) {
            return false;
        }
        
        $now = now();
        
        if ($this->valid_from && $now->lt($this->valid_from)) {
            return false;
        }
        
        if ($this->valid_until && $now->gt($this->valid_until)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取奖励类型文本
     */
    public function getRewardTypeTextAttribute(): string
    {
        $types = [
            'cash' => '现金奖励',
            'coupon' => '优惠券',
            'gift' => '实物礼品',
            'vip' => 'VIP权益',
            'points' => '积分奖励',
            'discount' => '折扣券',
            'service' => '服务权益'
        ];
        
        return $types[$this->reward_type] ?? '未知类型';
    }
    
    /**
     * 查询作用域 - 活跃奖励
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
    
    /**
     * 查询作用域 - 可用奖励
     */
    public function scopeAvailable(Builder $query): Builder
    {
        $now = now();
        return $query->where('is_active', true)
                    ->whereRaw('stock_quantity > used_quantity')
                    ->where(function ($q) use ($now) {
                        $q->whereNull('valid_from')
                          ->orWhere('valid_from', '<=', $now);
                    })
                    ->where(function ($q) use ($now) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>=', $now);
                    });
    }
    
    /**
     * 查询作用域 - 按奖励类型筛选
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('reward_type', $type);
    }
    
    /**
     * 查询作用域 - 按积分范围筛选
     */
    public function scopeByPointsRange(Builder $query, int $minPoints, int $maxPoints): Builder
    {
        return $query->whereBetween('required_points', [$minPoints, $maxPoints]);
    }
    
    /**
     * 查询作用域 - 用户可兑换的奖励
     */
    public function scopeAffordableFor(Builder $query, int $userPoints): Builder
    {
        return $query->where('required_points', '<=', $userPoints);
    }
    
    /**
     * 查询作用域 - 按排序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('required_points');
    }
    
    /**
     * 查询作用域 - 有库存的奖励
     */
    public function scopeInStock(Builder $query): Builder
    {
        return $query->whereRaw('stock_quantity > used_quantity');
    }
    
    /**
     * 检查用户是否可以兑换
     */
    public function canBeRedeemedBy(int $userPoints): bool
    {
        return $this->is_available && $userPoints >= $this->required_points;
    }
    
    /**
     * 兑换奖励（减少库存）
     */
    public function redeem(): bool
    {
        if (!$this->is_available) {
            return false;
        }
        
        $this->increment('used_quantity');
        return true;
    }
    
    /**
     * 退还奖励（增加库存）
     */
    public function refund(): bool
    {
        if ($this->used_quantity <= 0) {
            return false;
        }
        
        $this->decrement('used_quantity');
        return true;
    }
    
    /**
     * 增加库存
     */
    public function addStock(int $quantity): bool
    {
        $this->increment('stock_quantity', $quantity);
        return true;
    }
    
    /**
     * 减少库存
     */
    public function reduceStock(int $quantity): bool
    {
        if ($this->stock_quantity < $quantity) {
            return false;
        }
        
        $this->decrement('stock_quantity', $quantity);
        return true;
    }
    
    /**
     * 激活奖励
     */
    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }
    
    /**
     * 停用奖励
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }
    
    /**
     * 获取兑换统计
     */
    public function getRedemptionStats(): array
    {
        return [
            'total_redemptions' => $this->used_quantity,
            'remaining_stock' => $this->remaining_stock,
            'redemption_rate' => $this->stock_quantity > 0 ? 
                round(($this->used_quantity / $this->stock_quantity) * 100, 2) : 0,
            'total_points_spent' => $this->used_quantity * $this->required_points
        ];
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'config_id' => $this->config_id,
            'reward_name' => $this->reward_name,
            'reward_type' => $this->reward_type,
            'reward_type_text' => $this->reward_type_text,
            'reward_value' => $this->reward_value,
            'required_points' => $this->required_points,
            'stock_quantity' => $this->stock_quantity,
            'used_quantity' => $this->used_quantity,
            'remaining_stock' => $this->remaining_stock,
            'reward_image' => $this->reward_image_full_url,
            'description' => $this->description,
            'terms_conditions' => $this->terms_conditions,
            'is_active' => $this->is_active,
            'is_available' => $this->is_available,
            'sort_order' => $this->sort_order,
            'redemption_stats' => $this->getRedemptionStats(),
            'valid_from' => $this->valid_from?->format('Y-m-d H:i:s'),
            'valid_until' => $this->valid_until?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}