<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class UserRelation extends BaseCituiModel
{
    protected $table = 'user_relations';
    protected $primaryKey = 'relation_id';
    
    protected $fillable = [
        'user_id',
        'related_user_id',
        'relation_type',
        'status'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'related_user_id' => 'integer'
    ];
    
    /**
     * 关联发起用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联目标用户
     */
    public function relatedUser()
    {
        return $this->belongsTo(User::class, 'related_user_id', 'user_id');
    }
    
    /**
     * 查询作用域 - 关注关系
     */
    public function scopeFollow(Builder $query): Builder
    {
        return $query->where('relation_type', 'follow');
    }
    
    /**
     * 查询作用域 - 好友关系
     */
    public function scopeFriend(Builder $query): Builder
    {
        return $query->where('relation_type', 'friend');
    }
    
    /**
     * 查询作用域 - 拉黑关系
     */
    public function scopeBlock(Builder $query): Builder
    {
        return $query->where('relation_type', 'block');
    }
    
    /**
     * 查询作用域 - 邀请关系
     */
    public function scopeInvite(Builder $query): Builder
    {
        return $query->where('relation_type', 'invite');
    }
    
    /**
     * 查询作用域 - 已接受的关系
     */
    public function scopeAccepted(Builder $query): Builder
    {
        return $query->where('status', 'accepted');
    }
    
    /**
     * 查询作用域 - 待处理的关系
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * 查询作用域 - 已拒绝的关系
     */
    public function scopeRejected(Builder $query): Builder
    {
        return $query->where('status', 'rejected');
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 按目标用户筛选
     */
    public function scopeByRelatedUser(Builder $query, int $relatedUserId): Builder
    {
        return $query->where('related_user_id', $relatedUserId);
    }
    
    /**
     * 查询作用域 - 双向关系查询
     */
    public function scopeBetweenUsers(Builder $query, int $userId1, int $userId2): Builder
    {
        return $query->where(function ($q) use ($userId1, $userId2) {
            $q->where(function ($subQ) use ($userId1, $userId2) {
                $subQ->where('user_id', $userId1)
                     ->where('related_user_id', $userId2);
            })->orWhere(function ($subQ) use ($userId1, $userId2) {
                $subQ->where('user_id', $userId2)
                     ->where('related_user_id', $userId1);
            });
        });
    }
    
    /**
     * 检查是否为关注关系
     */
    public function isFollow(): bool
    {
        return $this->relation_type === 'follow';
    }
    
    /**
     * 检查是否为好友关系
     */
    public function isFriend(): bool
    {
        return $this->relation_type === 'friend';
    }
    
    /**
     * 检查是否为拉黑关系
     */
    public function isBlock(): bool
    {
        return $this->relation_type === 'block';
    }
    
    /**
     * 检查是否为邀请关系
     */
    public function isInvite(): bool
    {
        return $this->relation_type === 'invite';
    }
    
    /**
     * 检查关系是否已接受
     */
    public function isAccepted(): bool
    {
        return $this->status === 'accepted';
    }
    
    /**
     * 检查关系是否待处理
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }
    
    /**
     * 检查关系是否已拒绝
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }
    
    /**
     * 接受关系
     */
    public function accept(): bool
    {
        $this->status = 'accepted';
        return $this->save();
    }
    
    /**
     * 拒绝关系
     */
    public function reject(): bool
    {
        $this->status = 'rejected';
        return $this->save();
    }
    
    /**
     * 获取关系类型中文名称
     */
    public function getRelationTypeNameAttribute(): string
    {
        $typeNames = [
            'follow' => '关注',
            'friend' => '好友',
            'block' => '拉黑',
            'invite' => '邀请'
        ];
        
        return $typeNames[$this->relation_type] ?? '未知';
    }
    
    /**
     * 获取关系状态中文名称
     */
    public function getStatusNameAttribute(): string
    {
        $statusNames = [
            'pending' => '待处理',
            'accepted' => '已接受',
            'rejected' => '已拒绝'
        ];
        
        return $statusNames[$this->status] ?? '未知';
    }
    
    /**
     * 检查两个用户之间是否存在指定关系
     */
    public static function hasRelation(int $userId, int $relatedUserId, string $relationType): bool
    {
        return static::where('user_id', $userId)
                    ->where('related_user_id', $relatedUserId)
                    ->where('relation_type', $relationType)
                    ->where('status', 'accepted')
                    ->exists();
    }
    
    /**
     * 创建或更新关系
     */
    public static function createOrUpdateRelation(int $userId, int $relatedUserId, string $relationType, string $status = 'accepted'): self
    {
        return static::updateOrCreate(
            [
                'user_id' => $userId,
                'related_user_id' => $relatedUserId,
                'relation_type' => $relationType
            ],
            [
                'status' => $status
            ]
        );
    }
    
    /**
     * 删除关系
     */
    public static function removeRelation(int $userId, int $relatedUserId, string $relationType): bool
    {
        return static::where('user_id', $userId)
                    ->where('related_user_id', $relatedUserId)
                    ->where('relation_type', $relationType)
                    ->delete() > 0;
    }
}