<?php
declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Citui\AppStatisticsService;

class UpdateAppStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'citui:update-app-statistics 
                            {--app-ids=* : 指定要更新的APP ID列表}
                            {--all : 更新所有APP的统计}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新CitUI APP统计数据';

    protected AppStatisticsService $statisticsService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(AppStatisticsService $statisticsService)
    {
        parent::__construct();
        $this->statisticsService = $statisticsService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始更新APP统计数据...');

        try {
            $appIds = [];
            
            if ($this->option('all')) {
                $this->info('更新所有APP的统计数据');
            } elseif ($this->option('app-ids')) {
                $appIds = array_map('intval', $this->option('app-ids'));
                $this->info('更新指定APP的统计数据: ' . implode(', ', $appIds));
            } else {
                $this->error('请指定 --all 或 --app-ids 参数');
                return 1;
            }

            $result = $this->statisticsService->batchUpdateStatistics($appIds);

            if ($result['success']) {
                $data = $result['data'];
                $this->info("统计更新完成:");
                $this->info("- 总APP数量: {$data['total_apps']}");
                $this->info("- 成功更新: {$data['updated_count']}");
                $this->info("- 失败数量: {$data['error_count']}");

                if (!empty($data['errors'])) {
                    $this->warn('更新过程中的错误:');
                    foreach ($data['errors'] as $error) {
                        $this->warn("  - {$error}");
                    }
                }

                return 0;
            } else {
                $this->error('统计更新失败: ' . $result['message']);
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('命令执行失败: ' . $e->getMessage());
            return 1;
        }
    }
}