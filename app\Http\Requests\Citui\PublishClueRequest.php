<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class PublishClueRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'app_id' => 'required|integer|exists:ct_apps,app_id',
            'clue_title' => 'required|string|max:200',
            'clue_content' => 'required|string|max:5000',
            'clue_type' => 'required|string|in:task,bug,feature,reward,other',
            'difficulty_level' => 'required|string|in:easy,medium,hard,expert',
            'expected_reward' => 'required|numeric|min:0|max:999999.99',
            'risk_level' => 'required|string|in:low,medium,high,extreme',
            'steps' => 'required|array|min:1',
            'steps.*' => 'required|string|max:1000',
            'screenshots' => 'nullable|array|max:10',
            'screenshots.*' => 'image|mimes:jpeg,jpg,png,webp|max:2048',
            'tags' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'clue_type' => '线索类型',
            'difficulty_level' => '难度等级',
            'expected_reward' => '预期收益',
            'risk_level' => '风险等级',
            'steps' => '操作步骤',
            'steps.*' => '操作步骤',
            'tags' => '标签',
            'expires_at' => '过期时间'
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'clue_type.in' => '线索类型必须是：任务线索、Bug线索、功能线索、奖励线索、其他线索中的一种',
            'difficulty_level.in' => '难度等级必须是：简单、中等、困难、专家中的一种',
            'risk_level.in' => '风险等级必须是：低风险、中等风险、高风险、极高风险中的一种',
            'steps.min' => '至少需要提供一个操作步骤',
            'screenshots.max' => '最多只能上传10张截图',
            'expires_at.after' => '过期时间必须是未来时间'
        ]);
    }
}