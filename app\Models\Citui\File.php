<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class File extends BaseCituiModel
{
    protected $table = 'files';
    protected $primaryKey = 'file_id';
    
    protected $fillable = [
        'category_id',
        'uploader_id',
        'original_name',
        'stored_name',
        'file_path',
        'file_url',
        'file_size',
        'file_type',
        'file_extension',
        'file_hash',
        'width',
        'height',
        'duration',
        'upload_ip',
        'upload_device',
        'storage_type',
        'is_public',
        'is_deleted',
        'deleted_at'
    ];
    
    protected $casts = [
        'category_id' => 'integer',
        'uploader_id' => 'integer',
        'file_size' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'duration' => 'integer',
        'is_public' => 'boolean',
        'is_deleted' => 'boolean',
        'deleted_at' => 'datetime'
    ];
    
    /**
     * 关联文件分类
     */
    public function category()
    {
        return $this->belongsTo(FileCategory::class, 'category_id', 'category_id');
    }
    
    /**
     * 关联上传用户
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploader_id', 'user_id');
    }
    
    /**
     * 关联文件关系
     */
    public function relations()
    {
        return $this->hasMany(FileRelation::class, 'file_id', 'file_id');
    }
    
    /**
     * 查询作用域 - 公开文件
     */
    public function scopePublic(Builder $query): Builder
    {
        return $query->where('is_public', true);
    }
    
    /**
     * 查询作用域 - 私有文件
     */
    public function scopePrivate(Builder $query): Builder
    {
        return $query->where('is_public', false);
    }
    
    /**
     * 查询作用域 - 图片文件
     */
    public function scopeImages(Builder $query): Builder
    {
        return $query->whereIn('file_extension', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }
    
    /**
     * 查询作用域 - 按存储类型筛选
     */
    public function scopeByStorageType(Builder $query, string $storageType): Builder
    {
        return $query->where('storage_type', $storageType);
    }
    
    /**
     * 检查是否为图片
     */
    public function isImage(): bool
    {
        return in_array(strtolower($this->file_extension), ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }
    
    /**
     * 检查是否为视频
     */
    public function isVideo(): bool
    {
        return in_array(strtolower($this->file_extension), ['mp4', 'avi', 'mov', 'wmv']);
    }
    
    /**
     * 检查是否为音频
     */
    public function isAudio(): bool
    {
        return in_array(strtolower($this->file_extension), ['mp3', 'wav', 'aac', 'flac']);
    }
    
    /**
     * 获取格式化的文件大小
     */
    public function getFormattedSizeAttribute(): string
    {
        $size = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
    
    /**
     * 获取完整的文件URL
     */
    public function getFullUrlAttribute(): string
    {
        if (filter_var($this->file_url, FILTER_VALIDATE_URL)) {
            return $this->file_url;
        }
        
        return config('app.url') . '/' . ltrim($this->file_url, '/');
    }
}