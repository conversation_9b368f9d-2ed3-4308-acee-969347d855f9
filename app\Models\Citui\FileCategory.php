<?php
declare(strict_types=1);

namespace App\Models\Citui;

class FileCategory extends BaseCituiModel
{
    protected $table = 'file_categories';
    protected $primaryKey = 'category_id';
    
    protected $fillable = [
        'category_name',
        'category_code',
        'parent_id',
        'sort_order',
        'max_file_size',
        'allowed_extensions',
        'description',
        'is_active'
    ];
    
    protected $casts = [
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'max_file_size' => 'integer',
        'is_active' => 'boolean'
    ];
    
    /**
     * 关联文件
     */
    public function files()
    {
        return $this->hasMany(File::class, 'category_id', 'category_id');
    }
    
    /**
     * 获取允许的扩展名数组
     */
    public function getAllowedExtensionsArrayAttribute(): array
    {
        return $this->allowed_extensions ? explode(',', $this->allowed_extensions) : [];
    }
    
    /**
     * 检查文件扩展名是否允许
     */
    public function isExtensionAllowed(string $extension): bool
    {
        return in_array(strtolower($extension), $this->allowed_extensions_array);
    }
    
    /**
     * 检查文件大小是否允许
     */
    public function isSizeAllowed(int $fileSize): bool
    {
        return $fileSize <= $this->max_file_size;
    }
}