<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class PointRecord extends BaseCituiModel
{
    protected $table = 'point_records';
    protected $primaryKey = 'record_id';
    
    protected $fillable = [
        'user_id',
        'rule_id',
        'reward_config_id',
        'points',
        'record_type',
        'source_type',
        'source_id',
        'description',
        'balance_before',
        'balance_after',
        'status',
        'expires_at'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'rule_id' => 'integer',
        'reward_config_id' => 'integer',
        'points' => 'integer',
        'source_id' => 'integer',
        'balance_before' => 'integer',
        'balance_after' => 'integer',
        'expires_at' => 'datetime'
    ];
    
    protected $appends = [
        'record_type_text',
        'source_type_text',
        'status_text',
        'is_expired',
        'days_until_expiry'
    ];
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联积分规则
     */
    public function rule()
    {
        return $this->belongsTo(PointRule::class, 'rule_id', 'rule_id');
    }
    
    /**
     * 关联奖励配置
     */
    public function rewardConfig()
    {
        return $this->belongsTo(RewardConfig::class, 'reward_config_id', 'config_id');
    }
    
    /**
     * 多态关联 - 来源对象
     */
    public function source()
    {
        return $this->morphTo('source', 'source_type', 'source_id');
    }
    
    /**
     * 获取记录类型文本
     */
    public function getRecordTypeTextAttribute(): string
    {
        $types = [
            'earn' => '获得积分',
            'spend' => '消费积分',
            'expire' => '积分过期',
            'refund' => '积分退还',
            'adjust' => '积分调整'
        ];
        
        return $types[$this->record_type] ?? '未知类型';
    }
    
    /**
     * 获取来源类型文本
     */
    public function getSourceTypeTextAttribute(): string
    {
        $types = [
            'register' => '用户注册',
            'login' => '每日登录',
            'evaluation' => '评测报告',
            'clue' => '线索分享',
            'feedback' => '反馈提交',
            'invite' => '邀请用户',
            'reward' => '奖励兑换',
            'admin' => '管理员操作',
            'system' => '系统操作'
        ];
        
        return $types[$this->source_type] ?? '未知来源';
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => '待处理',
            'completed' => '已完成',
            'cancelled' => '已取消',
            'expired' => '已过期'
        ];
        
        return $statuses[$this->status] ?? '未知状态';
    }
    
    /**
     * 检查是否已过期
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }
    
    /**
     * 获取距离过期天数
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }
        
        return now()->diffInDays($this->expires_at, false);
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 按记录类型筛选
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('record_type', $type);
    }
    
    /**
     * 查询作用域 - 获得积分记录
     */
    public function scopeEarn(Builder $query): Builder
    {
        return $query->where('record_type', 'earn');
    }
    
    /**
     * 查询作用域 - 消费积分记录
     */
    public function scopeSpend(Builder $query): Builder
    {
        return $query->where('record_type', 'spend');
    }
    
    /**
     * 查询作用域 - 按来源类型筛选
     */
    public function scopeBySourceType(Builder $query, string $sourceType): Builder
    {
        return $query->where('source_type', $sourceType);
    }
    
    /**
     * 查询作用域 - 按状态筛选
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }
    
    /**
     * 查询作用域 - 已完成的记录
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }
    
    /**
     * 查询作用域 - 未过期的记录
     */
    public function scopeNotExpired(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }
    
    /**
     * 查询作用域 - 已过期的记录
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }
    
    /**
     * 查询作用域 - 今天的记录
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('created_at', Carbon::today());
    }
    
    /**
     * 查询作用域 - 本月的记录
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }
    
    /**
     * 查询作用域 - 积分范围筛选
     */
    public function scopePointsRange(Builder $query, int $minPoints, int $maxPoints): Builder
    {
        return $query->whereBetween('points', [$minPoints, $maxPoints]);
    }
    
    /**
     * 完成记录
     */
    public function complete(): bool
    {
        $this->status = 'completed';
        return $this->save();
    }
    
    /**
     * 取消记录
     */
    public function cancel(): bool
    {
        $this->status = 'cancelled';
        return $this->save();
    }
    
    /**
     * 设为过期
     */
    public function expire(): bool
    {
        $this->status = 'expired';
        return $this->save();
    }
    
    /**
     * 检查是否为正积分
     */
    public function isPositive(): bool
    {
        return $this->points > 0;
    }
    
    /**
     * 检查是否为负积分
     */
    public function isNegative(): bool
    {
        return $this->points < 0;
    }
    
    /**
     * 获取用户积分统计
     */
    public static function getUserPointsStats(int $userId, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);
        
        $records = static::byUser($userId)
                        ->completed()
                        ->where('created_at', '>=', $startDate)
                        ->get();
        
        $earnedRecords = $records->where('record_type', 'earn');
        $spentRecords = $records->where('record_type', 'spend');
        
        return [
            'total_earned' => $earnedRecords->sum('points'),
            'total_spent' => abs($spentRecords->sum('points')),
            'net_points' => $records->sum('points'),
            'earn_count' => $earnedRecords->count(),
            'spend_count' => $spentRecords->count(),
            'period_days' => $days,
            'daily_average' => $days > 0 ? round($records->sum('points') / $days, 2) : 0
        ];
    }
    
    /**
     * 创建积分记录
     */
    public static function createRecord(array $data): self
    {
        // 计算余额变化
        $user = User::find($data['user_id']);
        $balanceBefore = $user ? $user->available_points : 0;
        $balanceAfter = $balanceBefore + $data['points'];
        
        return static::create(array_merge($data, [
            'balance_before' => $balanceBefore,
            'balance_after' => $balanceAfter,
            'status' => $data['status'] ?? 'completed'
        ]));
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'record_id' => $this->record_id,
            'user_id' => $this->user_id,
            'user' => $this->user?->toApiArray(),
            'rule_id' => $this->rule_id,
            'rule' => $this->rule?->toApiArray(),
            'reward_config_id' => $this->reward_config_id,
            'reward_config' => $this->rewardConfig?->toApiArray(),
            'points' => $this->points,
            'record_type' => $this->record_type,
            'record_type_text' => $this->record_type_text,
            'source_type' => $this->source_type,
            'source_type_text' => $this->source_type_text,
            'source_id' => $this->source_id,
            'description' => $this->description,
            'balance_before' => $this->balance_before,
            'balance_after' => $this->balance_after,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'is_positive' => $this->isPositive(),
            'is_negative' => $this->isNegative(),
            'is_expired' => $this->is_expired,
            'days_until_expiry' => $this->days_until_expiry,
            'expires_at' => $this->expires_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}