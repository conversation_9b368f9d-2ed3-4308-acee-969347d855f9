<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class EvaluationLike extends BaseCituiModel
{
    protected $table = 'evaluation_likes';
    protected $primaryKey = 'like_id';
    
    // 只有创建时间，没有更新时间
    const UPDATED_AT = null;
    
    protected $fillable = [
        'report_id',
        'user_id',
        'like_type'
    ];
    
    protected $casts = [
        'report_id' => 'integer',
        'user_id' => 'integer'
    ];
    
    /**
     * 关联评测报告
     */
    public function report()
    {
        return $this->belongsTo(EvaluationReport::class, 'report_id', 'report_id');
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 查询作用域 - 按报告筛选
     */
    public function scopeByReport(Builder $query, int $reportId): Builder
    {
        return $query->where('report_id', $reportId);
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 点赞类型
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('like_type', $type);
    }
    
    /**
     * 查询作用域 - 点赞
     */
    public function scopeLike(Builder $query): Builder
    {
        return $query->where('like_type', 'like');
    }
    
    /**
     * 查询作用域 - 不喜欢
     */
    public function scopeDislike(Builder $query): Builder
    {
        return $query->where('like_type', 'dislike');
    }
    
    /**
     * 获取点赞类型文本
     */
    public function getLikeTypeTextAttribute(): string
    {
        $types = [
            'like' => '点赞',
            'dislike' => '不喜欢'
        ];
        
        return $types[$this->like_type] ?? '未知';
    }
    
    /**
     * 检查是否为点赞
     */
    public function isLike(): bool
    {
        return $this->like_type === 'like';
    }
    
    /**
     * 检查是否为不喜欢
     */
    public function isDislike(): bool
    {
        return $this->like_type === 'dislike';
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'like_id' => $this->like_id,
            'report_id' => $this->report_id,
            'user_id' => $this->user_id,
            'user' => $this->user?->toApiArray(),
            'like_type' => $this->like_type,
            'like_type_text' => $this->like_type_text,
            'is_like' => $this->isLike(),
            'is_dislike' => $this->isDislike(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s')
        ];
    }
}