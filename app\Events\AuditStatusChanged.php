<?php

namespace App\Events;

use App\Models\Citui\ContentAudit;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AuditStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ContentAudit $audit;
    public string $oldStatus;
    public string $newStatus;
    public ?int $operatorId;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(ContentAudit $audit, string $oldStatus, string $newStatus, ?int $operatorId = null)
    {
        $this->audit = $audit;
        $this->oldStatus = $oldStatus;
        $this->newStatus = $newStatus;
        $this->operatorId = $operatorId;
    }
}