<?php
declare(strict_types=1);

namespace App\Models\Citui;

class AuditLog extends BaseCituiModel
{
    protected $table = 'audit_logs';
    protected $primaryKey = 'log_id';
    
    protected $fillable = [
        'audit_id',
        'operator_id',
        'operator_type',
        'action_type',
        'old_status',
        'new_status',
        'action_data',
        'action_reason',
        'ip_address',
        'user_agent'
    ];
    
    protected $casts = [
        'audit_id' => 'integer',
        'operator_id' => 'integer',
        'action_data' => 'array'
    ];
    
    // 不使用updated_at字段
    const UPDATED_AT = null;
    
    /**
     * 关联审核记录
     */
    public function audit()
    {
        return $this->belongsTo(ContentAudit::class, 'audit_id', 'audit_id');
    }
    
    /**
     * 关联操作人（管理员）
     */
    public function operator()
    {
        return $this->belongsTo(AdminUser::class, 'operator_id', 'admin_id');
    }
}