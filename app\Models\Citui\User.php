<?php
declare(strict_types=1);

namespace App\Models\Citui;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class User extends Authenticatable
{
    use HasApiTokens;
    
    protected $table = 'users';
    protected $primaryKey = 'user_id';
    
    protected $fillable = [
        'phone',
        'password_hash',
        'nickname', 
        'real_name',
        'avatar_url',
        'gender',
        'birthday',
        'province',
        'city',
        'total_points',
        'available_points',
        'level',
        'status'
    ];
    
    protected $hidden = [
        'password_hash'
    ];
    
    protected $casts = [
        'birthday' => 'date',
        'total_points' => 'integer',
        'available_points' => 'integer',
        'level' => 'integer',
        'last_login_at' => 'datetime'
    ];
    
    /**
     * 获取用于认证的密码字段名
     */
    public function getAuthPassword()
    {
        return $this->password_hash;
    }
    
    /**
     * 设置密码
     */
    public function setPasswordAttribute($value): void
    {
        $this->attributes['password_hash'] = Hash::make($value);
    }
    
    /**
     * 验证密码
     */
    public function checkPassword(string $password): bool
    {
        return Hash::check($password, $this->password_hash);
    }
    
    /**
     * 关联评测报告
     */
    public function evaluationReports()
    {
        return $this->hasMany(EvaluationReport::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联放水线索
     */
    public function waterClues()
    {
        return $this->hasMany(WaterClue::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联积分记录
     */
    public function pointRecords()
    {
        return $this->hasMany(PointRecord::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联用户登录记录
     */
    public function loginRecords()
    {
        return $this->hasMany(UserLogin::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联用户关系（我关注的）
     */
    public function following()
    {
        return $this->hasMany(UserRelation::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联用户关系（关注我的）
     */
    public function followers()
    {
        return $this->hasMany(UserRelation::class, 'related_user_id', 'user_id');
    }
    
    /**
     * 关联上传的文件
     */
    public function uploadedFiles()
    {
        return $this->hasMany(File::class, 'uploader_id', 'user_id');
    }
    
    /**
     * 查询作用域 - 活跃用户
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }
    
    /**
     * 查询作用域 - 被封禁用户
     */
    public function scopeBanned(Builder $query): Builder
    {
        return $query->where('status', 'banned');
    }
    
    /**
     * 查询作用域 - 按等级筛选
     */
    public function scopeByLevel(Builder $query, int $level): Builder
    {
        return $query->where('level', $level);
    }
    
    /**
     * 查询作用域 - 按积分范围筛选
     */
    public function scopeByPointsRange(Builder $query, int $minPoints = 0, ?int $maxPoints = null): Builder
    {
        $query->where('total_points', '>=', $minPoints);
        
        if ($maxPoints !== null) {
            $query->where('total_points', '<=', $maxPoints);
        }
        
        return $query;
    }
    
    /**
     * 查询作用域 - 按地区筛选
     */
    public function scopeByLocation(Builder $query, ?string $province = null, ?string $city = null): Builder
    {
        if ($province) {
            $query->where('province', $province);
        }
        
        if ($city) {
            $query->where('city', $city);
        }
        
        return $query;
    }
    
    /**
     * 获取用户头像URL（带默认头像）
     */
    public function getAvatarAttribute(): string
    {
        return $this->avatar_url ?: '/images/default-avatar.png';
    }
    
    /**
     * 获取用户显示名称
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->nickname ?: $this->real_name ?: '用户' . $this->user_id;
    }
    
    /**
     * 获取用户等级名称
     */
    public function getLevelNameAttribute(): string
    {
        $levelNames = [
            1 => '新手',
            2 => '初级',
            3 => '中级',
            4 => '高级',
            5 => '专家',
            6 => '大师'
        ];
        
        return $levelNames[$this->level] ?? '未知';
    }
    
    /**
     * 获取用户年龄
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birthday ? Carbon::parse($this->birthday)->age : null;
    }
    
    /**
     * 检查用户是否活跃
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
    
    /**
     * 检查用户是否被封禁
     */
    public function isBanned(): bool
    {
        return $this->status === 'banned';
    }
    
    /**
     * 检查用户是否为管理员
     */
    public function isAdmin(): bool
    {
        // 可以根据实际需求调整管理员判断逻辑
        return $this->level >= 6 || $this->status === 'admin';
    }
    
    /**
     * 检查用户手机号是否已验证
     */
    public function isPhoneVerified(): bool
    {
        // 如果用户能够注册成功，说明手机号已验证
        // 也可以添加专门的验证状态字段
        return !empty($this->phone) && $this->status !== 'unverified';
    }
    
    /**
     * 增加积分
     */
    public function addPoints(int $points, string $source = 'manual', array $context = []): bool
    {
        $balanceBefore = $this->available_points;
        
        $this->total_points += $points;
        $this->available_points += $points;
        
        if ($this->save()) {
            // 记录积分变化
            PointRecord::create([
                'user_id' => $this->user_id,
                'points' => $points,
                'record_type' => 'earn',
                'source_type' => $source,
                'source_id' => $context['source_id'] ?? null,
                'description' => $context['description'] ?? "获得{$points}积分：{$source}",
                'balance_before' => $balanceBefore,
                'balance_after' => $this->available_points,
                'status' => 'completed'
            ]);
            
            // 检查是否需要升级
            $this->checkLevelUp();
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 扣除积分
     */
    public function deductPoints(int $points, string $source = 'manual', array $context = []): bool
    {
        if ($this->available_points < $points) {
            return false;
        }
        
        $balanceBefore = $this->available_points;
        $this->available_points -= $points;
        
        if ($this->save()) {
            // 记录积分变化
            PointRecord::create([
                'user_id' => $this->user_id,
                'points' => -$points,
                'record_type' => 'spend',
                'source_type' => $source,
                'source_id' => $context['source_id'] ?? null,
                'description' => $context['description'] ?? "扣除{$points}积分：{$source}",
                'balance_before' => $balanceBefore,
                'balance_after' => $this->available_points,
                'status' => 'completed'
            ]);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查并升级用户等级
     */
    protected function checkLevelUp(): void
    {
        $levelThresholds = [
            1 => 0,
            2 => 100,
            3 => 500,
            4 => 1500,
            5 => 5000,
            6 => 15000
        ];
        
        $newLevel = 1;
        foreach ($levelThresholds as $level => $threshold) {
            if ($this->total_points >= $threshold) {
                $newLevel = $level;
            }
        }
        
        if ($newLevel > $this->level) {
            $this->level = $newLevel;
            $this->save();
        }
    }
    
    /**
     * 更新最后登录时间
     */
    public function updateLastLogin(?string $ip = null, ?string $device = null): void
    {
        $this->last_login_at = now();
        $this->last_login_ip = $ip;
        $this->save();
    }
    
    /**
     * 获取用户统计数据
     */
    public function getStatistics(): array
    {
        return [
            'evaluation_count' => $this->evaluationReports()->count(),
            'clue_count' => $this->waterClues()->count(),
            'total_points' => $this->total_points,
            'available_points' => $this->available_points,
            'level' => $this->level,
            'level_name' => $this->level_name,
            'following_count' => $this->following()->where('relation_type', 'follow')->count(),
            'followers_count' => $this->followers()->where('relation_type', 'follow')->count(),
            'join_days' => $this->created_at ? $this->created_at->diffInDays(now()) : 0
        ];
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'user_id' => $this->user_id,
            'phone' => $this->phone,
            'nickname' => $this->nickname,
            'real_name' => $this->real_name,
            'avatar' => $this->avatar,
            'avatar_url' => $this->avatar_url,
            'display_name' => $this->display_name,
            'gender' => $this->gender,
            'birthday' => $this->birthday?->format('Y-m-d'),
            'age' => $this->age,
            'province' => $this->province,
            'city' => $this->city,
            'total_points' => $this->total_points,
            'available_points' => $this->available_points,
            'level' => $this->level,
            'level_name' => $this->level_name,
            'status' => $this->status,
            'is_active' => $this->isActive(),
            'is_banned' => $this->isBanned(),
            'last_login_at' => $this->last_login_at?->format('Y-m-d H:i:s'),
            'last_login_ip' => $this->last_login_ip,
            'statistics' => $this->getStatistics(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}