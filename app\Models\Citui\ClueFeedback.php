<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class ClueFeedback extends BaseCituiModel
{
    protected $table = 'clue_feedbacks';
    protected $primaryKey = 'feedback_id';
    
    protected $fillable = [
        'clue_id',
        'user_id',
        'feedback_type',
        'feedback_content',
        'result_status',
        'actual_reward',
        'execution_time',
        'screenshots',
        'rating',
        'is_anonymous',
        'status'
    ];
    
    protected $casts = [
        'clue_id' => 'integer',
        'user_id' => 'integer',
        'actual_reward' => 'decimal:2',
        'execution_time' => 'integer',
        'screenshots' => 'array',
        'rating' => 'integer',
        'is_anonymous' => 'boolean'
    ];
    
    protected $appends = [
        'screenshots_full_urls',
        'feedback_type_text',
        'result_status_text',
        'status_text',
        'execution_time_text',
        'rating_stars'
    ];
    
    /**
     * 关联线索
     */
    public function clue()
    {
        return $this->belongsTo(WaterClue::class, 'clue_id', 'clue_id');
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 获取完整截图URLs
     */
    public function getScreenshotsFullUrlsAttribute(): array
    {
        if (!$this->screenshots || !is_array($this->screenshots)) {
            return [];
        }
        
        return array_map(function ($screenshot) {
            if (str_starts_with($screenshot, 'http')) {
                return $screenshot;
            }
            return config('app.url') . $screenshot;
        }, $this->screenshots);
    }
    
    /**
     * 获取反馈类型文本
     */
    public function getFeedbackTypeTextAttribute(): string
    {
        $types = [
            'success' => '成功反馈',
            'failure' => '失败反馈',
            'partial' => '部分成功',
            'question' => '疑问反馈',
            'suggestion' => '建议反馈'
        ];
        
        return $types[$this->feedback_type] ?? '未知';
    }
    
    /**
     * 获取结果状态文本
     */
    public function getResultStatusTextAttribute(): string
    {
        $statuses = [
            'success' => '成功',
            'failure' => '失败',
            'partial' => '部分成功',
            'pending' => '进行中'
        ];
        
        return $statuses[$this->result_status] ?? '未知';
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ];
        
        return $statuses[$this->status] ?? '未知';
    }
    
    /**
     * 获取执行时间文本
     */
    public function getExecutionTimeTextAttribute(): string
    {
        if (!$this->execution_time) {
            return '未知';
        }
        
        $hours = intval($this->execution_time / 60);
        $minutes = $this->execution_time % 60;
        
        if ($hours > 0) {
            return "{$hours}小时{$minutes}分钟";
        } else {
            return "{$minutes}分钟";
        }
    }
    
    /**
     * 获取评分星级
     */
    public function getRatingStarsAttribute(): array
    {
        $rating = $this->rating;
        $fullStars = $rating;
        $emptyStars = 5 - $rating;
        
        return [
            'full' => $fullStars,
            'empty' => $emptyStars,
            'rating' => $rating
        ];
    }
    
    /**
     * 查询作用域 - 按线索筛选
     */
    public function scopeByClue(Builder $query, int $clueId): Builder
    {
        return $query->where('clue_id', $clueId);
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 按反馈类型筛选
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('feedback_type', $type);
    }
    
    /**
     * 查询作用域 - 成功反馈
     */
    public function scopeSuccess(Builder $query): Builder
    {
        return $query->where('result_status', 'success');
    }
    
    /**
     * 查询作用域 - 失败反馈
     */
    public function scopeFailure(Builder $query): Builder
    {
        return $query->where('result_status', 'failure');
    }
    
    /**
     * 查询作用域 - 已审核通过
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }
    
    /**
     * 查询作用域 - 待审核
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * 查询作用域 - 非匿名反馈
     */
    public function scopeNotAnonymous(Builder $query): Builder
    {
        return $query->where('is_anonymous', false);
    }
    
    /**
     * 查询作用域 - 按评分筛选
     */
    public function scopeByRating(Builder $query, int $rating): Builder
    {
        return $query->where('rating', $rating);
    }
    
    /**
     * 查询作用域 - 按评分范围筛选
     */
    public function scopeByRatingRange(Builder $query, int $minRating, int $maxRating): Builder
    {
        return $query->whereBetween('rating', [$minRating, $maxRating]);
    }
    
    /**
     * 审核通过
     */
    public function approve(): bool
    {
        $this->status = 'approved';
        return $this->save();
    }
    
    /**
     * 审核拒绝
     */
    public function reject(): bool
    {
        $this->status = 'rejected';
        return $this->save();
    }
    
    /**
     * 检查是否为成功反馈
     */
    public function isSuccess(): bool
    {
        return $this->result_status === 'success';
    }
    
    /**
     * 检查是否为失败反馈
     */
    public function isFailure(): bool
    {
        return $this->result_status === 'failure';
    }
    
    /**
     * 检查是否为匿名反馈
     */
    public function isAnonymous(): bool
    {
        return $this->is_anonymous;
    }
    
    /**
     * 检查是否已审核通过
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'feedback_id' => $this->feedback_id,
            'clue_id' => $this->clue_id,
            'clue' => $this->clue?->toApiArray(),
            'user_id' => $this->user_id,
            'user' => $this->is_anonymous ? null : $this->user?->toApiArray(),
            'feedback_type' => $this->feedback_type,
            'feedback_type_text' => $this->feedback_type_text,
            'feedback_content' => $this->feedback_content,
            'result_status' => $this->result_status,
            'result_status_text' => $this->result_status_text,
            'actual_reward' => $this->actual_reward,
            'execution_time' => $this->execution_time,
            'execution_time_text' => $this->execution_time_text,
            'screenshots' => $this->screenshots_full_urls,
            'rating' => $this->rating,
            'rating_stars' => $this->rating_stars,
            'is_anonymous' => $this->is_anonymous,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'is_success' => $this->isSuccess(),
            'is_failure' => $this->isFailure(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}