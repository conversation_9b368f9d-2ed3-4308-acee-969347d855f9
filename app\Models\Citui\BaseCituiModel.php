<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use DateTimeInterface;
use Carbon\Carbon;

abstract class BaseCituiModel extends Model
{
    protected $connection = 'mysql';
    
    // 统一的时间戳字段
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    
    /**
     * 统一的时间戳格式
     */
    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('Y-m-d H:i:s');
    }
    
    /**
     * 获取表的缓存键前缀
     */
    protected function getCachePrefix(): string
    {
        return 'citui:' . $this->getTable() . ':';
    }
    
    /**
     * 清除模型相关缓存
     */
    public function clearModelCache(): void
    {
        $prefix = $this->getCachePrefix();
        Cache::forget($prefix . 'all');
        Cache::forget($prefix . 'count');
        if ($this->getKey()) {
            Cache::forget($prefix . $this->getKey());
        }
    }
    
    /**
     * 模型事件：保存后清除缓存
     */
    protected static function booted(): void
    {
        static::saved(function ($model) {
            $model->clearModelCache();
        });
        
        static::deleted(function ($model) {
            $model->clearModelCache();
        });
    }
    
    /**
     * 通用的查询作用域 - 活跃状态
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }
    
    /**
     * 通用的查询作用域 - 非活跃状态
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', '!=', 'active');
    }
    
    /**
     * 通用的查询作用域 - 按创建时间排序
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'desc');
    }
    
    /**
     * 通用的查询作用域 - 按创建时间正序
     */
    public function scopeOldest(Builder $query): Builder
    {
        return $query->orderBy('created_at', 'asc');
    }
    
    /**
     * 通用的查询作用域 - 分页
     */
    public function scopePaginated(Builder $query, int $page = 1, int $perPage = 15): Builder
    {
        return $query->skip(($page - 1) * $perPage)->take($perPage);
    }
    
    /**
     * 通用的查询作用域 - 日期范围
     */
    public function scopeDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate) {
            $query->where('created_at', '>=', Carbon::parse($startDate)->startOfDay());
        }
        
        if ($endDate) {
            $query->where('created_at', '<=', Carbon::parse($endDate)->endOfDay());
        }
        
        return $query;
    }
    
    /**
     * 通用的查询作用域 - 今日数据
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('created_at', Carbon::today());
    }
    
    /**
     * 通用的查询作用域 - 本周数据
     */
    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('created_at', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }
    
    /**
     * 通用的查询作用域 - 本月数据
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }
    
    /**
     * 通用的查询作用域 - 软删除支持
     */
    public function scopeNotDeleted(Builder $query): Builder
    {
        if (in_array('is_deleted', $this->fillable) || isset($this->attributes['is_deleted'])) {
            return $query->where('is_deleted', 0);
        }
        return $query;
    }
    
    /**
     * 通用的查询作用域 - 已删除数据
     */
    public function scopeOnlyDeleted(Builder $query): Builder
    {
        if (in_array('is_deleted', $this->fillable) || isset($this->attributes['is_deleted'])) {
            return $query->where('is_deleted', 1);
        }
        return $query;
    }
    
    /**
     * 获取格式化的创建时间
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at ? $this->created_at->format('Y-m-d H:i:s') : '';
    }
    
    /**
     * 获取格式化的更新时间
     */
    public function getFormattedUpdatedAtAttribute(): string
    {
        return $this->updated_at ? $this->updated_at->format('Y-m-d H:i:s') : '';
    }
    
    /**
     * 获取相对时间（多久前）
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at ? $this->created_at->diffForHumans() : '';
    }
    
    /**
     * 软删除方法
     */
    public function softDelete(): bool
    {
        if (in_array('is_deleted', $this->fillable)) {
            $this->is_deleted = 1;
            $this->deleted_at = now();
            return $this->save();
        }
        return false;
    }
    
    /**
     * 恢复软删除
     */
    public function restore(): bool
    {
        if (in_array('is_deleted', $this->fillable)) {
            $this->is_deleted = 0;
            $this->deleted_at = null;
            return $this->save();
        }
        return false;
    }
    
    /**
     * 检查是否已软删除
     */
    public function isDeleted(): bool
    {
        return isset($this->attributes['is_deleted']) && $this->attributes['is_deleted'] == 1;
    }
    
    /**
     * 批量更新状态
     */
    public static function batchUpdateStatus(array $ids, string $status): int
    {
        return static::whereIn(static::make()->getKeyName(), $ids)
                    ->update(['status' => $status, 'updated_at' => now()]);
    }
    
    /**
     * 获取统计数据
     */
    public static function getStatistics(): array
    {
        $model = new static();
        $cacheKey = $model->getCachePrefix() . 'statistics';
        
        return Cache::remember($cacheKey, 300, function () use ($model) {
            $total = $model->count();
            $today = $model->today()->count();
            $thisWeek = $model->thisWeek()->count();
            $thisMonth = $model->thisMonth()->count();
            
            $stats = [
                'total' => $total,
                'today' => $today,
                'this_week' => $thisWeek,
                'this_month' => $thisMonth,
            ];
            
            // 如果有状态字段，统计各状态数量
            if (in_array('status', $model->fillable)) {
                $statusStats = $model->selectRaw('status, COUNT(*) as count')
                                   ->groupBy('status')
                                   ->pluck('count', 'status')
                                   ->toArray();
                $stats['by_status'] = $statusStats;
            }
            
            return $stats;
        });
    }
}