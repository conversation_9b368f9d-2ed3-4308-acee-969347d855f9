<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class AppCategory extends BaseCituiModel
{
    protected $table = 'app_categories';
    protected $primaryKey = 'category_id';
    
    protected $fillable = [
        'parent_id',
        'category_name',
        'category_icon',
        'description',
        'sort_order',
        'is_active'
    ];
    
    protected $casts = [
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean'
    ];
    
    protected $appends = [
        'icon_full_url',
        'apps_count',
        'level'
    ];
    
    /**
     * 关联父分类
     */
    public function parent()
    {
        return $this->belongsTo(static::class, 'parent_id', 'category_id');
    }
    
    /**
     * 关联子分类
     */
    public function children()
    {
        return $this->hasMany(static::class, 'parent_id', 'category_id')
                    ->where('is_active', true)
                    ->orderBy('sort_order');
    }
    
    /**
     * 关联所有子分类（递归）
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }
    
    /**
     * 关联APP
     */
    public function apps()
    {
        return $this->hasMany(App::class, 'category_id', 'category_id');
    }
    
    /**
     * 关联活跃APP
     */
    public function activeApps()
    {
        return $this->apps()->where('status', 'active');
    }
    
    /**
     * 获取完整图标URL
     */
    public function getIconFullUrlAttribute(): string
    {
        if (!$this->category_icon) {
            return config('app.url') . '/images/default-category-icon.png';
        }
        
        if (str_starts_with($this->category_icon, 'http')) {
            return $this->category_icon;
        }
        
        return config('app.url') . $this->category_icon;
    }
    
    /**
     * 获取APP数量
     */
    public function getAppsCountAttribute(): int
    {
        return $this->activeApps()->count();
    }
    
    /**
     * 获取分类层级
     */
    public function getLevelAttribute(): int
    {
        $level = 1;
        $parent = $this->parent;
        
        while ($parent) {
            $level++;
            $parent = $parent->parent;
        }
        
        return $level;
    }
    
    /**
     * 查询作用域 - 活跃分类
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
    
    /**
     * 查询作用域 - 顶级分类
     */
    public function scopeTopLevel(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }
    
    /**
     * 查询作用域 - 子分类
     */
    public function scopeChildren(Builder $query, int $parentId): Builder
    {
        return $query->where('parent_id', $parentId);
    }
    
    /**
     * 查询作用域 - 按排序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('category_name');
    }
    
    /**
     * 查询作用域 - 有APP的分类
     */
    public function scopeHasApps(Builder $query): Builder
    {
        return $query->whereHas('activeApps');
    }
    
    /**
     * 检查是否为顶级分类
     */
    public function isTopLevel(): bool
    {
        return is_null($this->parent_id);
    }
    
    /**
     * 检查是否有子分类
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }
    
    /**
     * 获取所有祖先分类
     */
    public function getAncestors(): array
    {
        $ancestors = [];
        $parent = $this->parent;
        
        while ($parent) {
            array_unshift($ancestors, $parent);
            $parent = $parent->parent;
        }
        
        return $ancestors;
    }
    
    /**
     * 获取分类路径
     */
    public function getCategoryPath(): string
    {
        $ancestors = $this->getAncestors();
        $path = array_map(fn($category) => $category->category_name, $ancestors);
        $path[] = $this->category_name;
        
        return implode(' > ', $path);
    }
    
    /**
     * 获取所有后代分类ID
     */
    public function getDescendantIds(): array
    {
        $ids = [];
        
        foreach ($this->children as $child) {
            $ids[] = $child->category_id;
            $ids = array_merge($ids, $child->getDescendantIds());
        }
        
        return $ids;
    }
    
    /**
     * 获取分类树
     */
    public static function getTree(): array
    {
        $categories = static::active()
                           ->ordered()
                           ->with(['children' => function ($query) {
                               $query->active()->ordered();
                           }])
                           ->topLevel()
                           ->get();
        
        return $categories->map(function ($category) {
            return $category->toTreeArray();
        })->toArray();
    }
    
    /**
     * 转换为树形数组
     */
    public function toTreeArray(): array
    {
        return [
            'category_id' => $this->category_id,
            'category_name' => $this->category_name,
            'category_icon' => $this->icon_full_url,
            'description' => $this->description,
            'apps_count' => $this->apps_count,
            'level' => $this->level,
            'children' => $this->children->map(function ($child) {
                return $child->toTreeArray();
            })->toArray()
        ];
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'category_id' => $this->category_id,
            'parent_id' => $this->parent_id,
            'category_name' => $this->category_name,
            'category_icon' => $this->icon_full_url,
            'description' => $this->description,
            'sort_order' => $this->sort_order,
            'is_active' => $this->is_active,
            'apps_count' => $this->apps_count,
            'level' => $this->level,
            'category_path' => $this->getCategoryPath(),
            'is_top_level' => $this->isTopLevel(),
            'has_children' => $this->hasChildren(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}