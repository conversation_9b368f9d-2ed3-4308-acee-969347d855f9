# AuthService 认证服务实现说明

## 概述

AuthService 是 CitUI Laravel 后端 API 系统的核心认证服务，负责处理用户注册、登录、短信验证码等功能。

## 主要功能

### 1. 用户注册 (register)
- 验证手机号格式和短信验证码
- 检查手机号是否已注册
- 创建用户记录
- 发放注册奖励积分
- 生成 API Token
- 记录登录日志

### 2. 用户登录 (login)
- 支持密码登录和短信验证码登录
- 验证用户状态（是否被禁用）
- 生成 API Token
- 更新最后登录时间
- 发放每日登录奖励积分
- 记录登录日志

### 3. 短信验证码 (sendSms/verifySms)
- 发送短信验证码（支持注册、登录、重置密码等场景）
- 验证码有效期 5 分钟
- 频率限制：每小时最多 5 次
- 支持验证码验证和过期处理

### 4. 用户登出 (logout)
- 撤销当前 Token
- 更新登录记录的登出时间

### 5. Token 刷新 (refreshToken)
- 撤销旧 Token
- 生成新 Token
- 验证用户状态

## 技术特性

### 安全性
- 手机号格式验证（正则表达式：`/^1[3-9]\d{9}$/`）
- 密码哈希存储（使用 Laravel Hash）
- Token 认证（Laravel Sanctum）
- 短信验证码缓存存储
- 频率限制防护

### 数据完整性
- 数据库事务处理
- 数据验证（使用 Laravel Validation）
- 异常处理和日志记录

### 积分系统集成
- 注册奖励积分自动发放
- 每日登录奖励积分
- 基于积分规则的动态计算

### 日志记录
- 操作日志记录
- 登录失败记录
- 异常处理日志

## 依赖关系

### 模型依赖
- `User` - 用户模型
- `UserLogin` - 用户登录记录模型
- `PointRecord` - 积分记录模型
- `PointRule` - 积分规则模型

### 服务依赖
- `BaseCituiService` - 基础服务类
- Laravel Sanctum - Token 认证
- Laravel Cache - 验证码缓存
- Laravel Hash - 密码哈希

## 配置要求

### 环境变量
```env
# 短信服务配置（需要根据实际短信服务商配置）
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your_access_key
SMS_SECRET_KEY=your_secret_key

# Token 过期时间（分钟）
SANCTUM_EXPIRATION=525600  # 1年
```

### 缓存配置
- 需要配置 Redis 或其他缓存驱动
- 用于存储短信验证码和频率限制

## API 响应格式

### 成功响应
```json
{
    "user": {
        "user_id": 1,
        "phone": "13812345678",
        "nickname": "用户昵称",
        // ... 其他用户信息
    },
    "token": "1|abc123...",
    "token_type": "Bearer",
    "expires_in": 525600
}
```

### 错误响应
```json
{
    "message": "错误信息",
    "errors": {
        "field": ["具体错误描述"]
    }
}
```

## 使用示例

### 发送注册验证码
```php
$authService = new AuthService();
$result = $authService->sendSms('13812345678', 'register');
```

### 用户注册
```php
$data = [
    'phone' => '13812345678',
    'sms_code' => '123456',
    'password' => 'password123',
    'nickname' => '用户昵称'
];
$result = $authService->register($data);
```

### 用户登录
```php
// 密码登录
$data = [
    'phone' => '13812345678',
    'password' => 'password123'
];
$result = $authService->login($data);

// 短信登录
$data = [
    'phone' => '13812345678',
    'sms_code' => '123456'
];
$result = $authService->login($data);
```

## 测试覆盖

已实现的测试用例：
- 类和方法存在性测试
- 手机号格式验证测试
- 短信验证码格式测试
- 基础功能逻辑测试

## 扩展建议

1. **短信服务集成**：集成实际的短信服务提供商（阿里云、腾讯云等）
2. **多因素认证**：支持邮箱验证、图形验证码等
3. **设备管理**：记录和管理用户登录设备
4. **安全增强**：IP 白名单、异常登录检测等
5. **性能优化**：缓存策略优化、数据库查询优化

## 注意事项

1. 生产环境需要配置实际的短信服务
2. 需要根据业务需求调整积分规则
3. 建议定期清理过期的验证码缓存
4. 监控登录失败率，及时发现异常
5. 定期更新安全策略和密码复杂度要求