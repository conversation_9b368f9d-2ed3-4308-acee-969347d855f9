<?php

declare(strict_types=1);

namespace App\Http\Requests\Citui;

use App\Models\Citui\App;
use App\Models\Citui\EvaluationReport;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class SubmitEvaluationRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $userId = auth()->id();
        
        return [
            'app_id' => [
                'required',
                'integer',
                'exists:ct_apps,app_id',
                function ($attribute, $value, $fail) use ($userId) {
                    // 检查APP是否可用
                    $app = App::find($value);
                    if ($app && $app->status !== 'active') {
                        $fail('选择的APP当前不可用');
                    }
                    
                    // 检查是否已提交过该APP的评测报告
                    if ($userId) {
                        $existingReport = EvaluationReport::where('user_id', $userId)
                                                        ->where('app_id', $value)
                                                        ->where('status', '!=', 'rejected')
                                                        ->exists();
                        if ($existingReport) {
                            $fail('您已经提交过该APP的评测报告');
                        }
                    }
                }
            ],
            'report_title' => [
                'required',
                'string',
                'min:5',
                'max:200',
                function ($attribute, $value, $fail) {
                    // 检查标题是否包含敏感词
                    if ($this->containsSensitiveWords($value)) {
                        $fail('报告标题包含敏感词汇');
                    }
                    
                    // 检查标题重复性
                    if ($this->isDuplicateTitle($value)) {
                        $fail('相似的报告标题已存在，请使用不同的标题');
                    }
                }
            ],
            'report_content' => [
                'required',
                'string',
                'min:50',
                'max:5000',
                function ($attribute, $value, $fail) {
                    // 检查内容是否包含敏感词
                    if ($this->containsSensitiveWords($value)) {
                        $fail('报告内容包含敏感词汇');
                    }
                    
                    // 检查内容质量（简单的重复字符检测）
                    if ($this->hasLowQualityContent($value)) {
                        $fail('报告内容质量不符合要求，请提供更详细的评测内容');
                    }
                }
            ],
            'task_description' => 'nullable|string|max:1000',
            'completion_time' => [
                'nullable',
                'integer',
                'min:1',
                'max:10080', // 最大7天（分钟）
                function ($attribute, $value, $fail) {
                    // 根据难度等级验证完成时间的合理性
                    $difficulty = $this->input('difficulty_level');
                    if ($difficulty && $value) {
                        $minTimes = [
                            'easy' => 5,     // 简单任务至少5分钟
                            'medium' => 15,  // 中等任务至少15分钟
                            'hard' => 30,    // 困难任务至少30分钟
                            'expert' => 60   // 专家任务至少60分钟
                        ];
                        
                        if (isset($minTimes[$difficulty]) && $value < $minTimes[$difficulty]) {
                            $fail("根据难度等级，完成时间不应少于{$minTimes[$difficulty]}分钟");
                        }
                    }
                }
            ],
            'difficulty_level' => 'required|in:easy,medium,hard,expert',
            'rating' => [
                'required',
                'integer',
                'between:1,5',
                function ($attribute, $value, $fail) {
                    // 检查评分与内容的一致性
                    $content = $this->input('report_content', '');
                    $pros = $this->input('pros', '');
                    $cons = $this->input('cons', '');
                    
                    // 高分评价应该有优点描述
                    if ($value >= 4 && empty($pros)) {
                        $fail('给出高分评价时，请在优点中说明理由');
                    }
                    
                    // 低分评价应该有缺点描述
                    if ($value <= 2 && empty($cons)) {
                        $fail('给出低分评价时，请在缺点中说明理由');
                    }
                }
            ],
            'pros' => [
                'nullable',
                'string',
                'max:1000',
                function ($attribute, $value, $fail) {
                    if ($value && $this->containsSensitiveWords($value)) {
                        $fail('优点描述包含敏感词汇');
                    }
                }
            ],
            'cons' => [
                'nullable',
                'string',
                'max:1000',
                function ($attribute, $value, $fail) {
                    if ($value && $this->containsSensitiveWords($value)) {
                        $fail('缺点描述包含敏感词汇');
                    }
                }
            ],
            'suggestions' => [
                'nullable',
                'string',
                'max:1000',
                function ($attribute, $value, $fail) {
                    if ($value && $this->containsSensitiveWords($value)) {
                        $fail('建议内容包含敏感词汇');
                    }
                }
            ],
            'screenshots' => [
                'nullable',
                'array',
                'max:5',
                function ($attribute, $value, $fail) use ($userId) {
                    // 检查提交频率限制
                    if ($userId && $this->exceedsSubmissionLimit($userId)) {
                        $fail('今日提交报告数量已达上限');
                    }
                }
            ],
            'screenshots.*' => [
                'string',
                'max:500',
                function ($attribute, $value, $fail) {
                    // 验证截图URL或文件ID的有效性
                    if (is_numeric($value)) {
                        // 如果是文件ID，检查文件是否存在且为图片
                        $file = \App\Models\Citui\File::find($value);
                        if (!$file || !$file->isImage()) {
                            $fail('截图文件无效或不是图片格式');
                        }
                    } elseif (filter_var($value, FILTER_VALIDATE_URL)) {
                        // 如果是URL，检查是否为图片URL
                        $extension = pathinfo(parse_url($value, PHP_URL_PATH), PATHINFO_EXTENSION);
                        if (!in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                            $fail('截图URL必须指向有效的图片文件');
                        }
                    } else {
                        $fail('截图必须是有效的文件ID或图片URL');
                    }
                }
            ]
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'app_id' => 'APP',
            'report_title' => '报告标题',
            'report_content' => '报告内容',
            'task_description' => '任务描述',
            'completion_time' => '完成时间',
            'difficulty_level' => '难度等级',
            'rating' => '评分',
            'pros' => '优点',
            'cons' => '缺点',
            'suggestions' => '建议',
            'screenshots' => '截图',
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'app_id.exists' => '选择的APP不存在',
            'report_title.min' => '报告标题至少需要5个字符',
            'report_content.min' => '报告内容至少需要50个字符',
            'difficulty_level.in' => '难度等级必须是：简单、中等、困难、专家之一',
            'rating.between' => '评分必须在1-5星之间',
            'completion_time.max' => '完成时间不能超过7天',
            'screenshots.max' => '最多只能上传5张截图',
        ]);
    }

    /**
     * 检查是否包含敏感词
     */
    protected function containsSensitiveWords(string $content): bool
    {
        // 简单的敏感词检测，实际项目中应该使用更完善的敏感词库
        $sensitiveWords = [
            '刷单', '作弊', '外挂', '破解', '盗版', '违法', '诈骗', '赌博',
            '色情', '暴力', '恐怖', '政治', '反动', '邪教', '毒品'
        ];
        
        foreach ($sensitiveWords as $word) {
            if (strpos($content, $word) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查标题重复性
     */
    protected function isDuplicateTitle(string $title): bool
    {
        // 检查是否存在相似的标题（使用简单的相似度算法）
        $existingTitles = EvaluationReport::where('status', '!=', 'rejected')
                                        ->pluck('report_title')
                                        ->toArray();
        
        foreach ($existingTitles as $existingTitle) {
            $similarity = 0;
            similar_text($title, $existingTitle, $similarity);
            if ($similarity > 80) { // 相似度超过80%认为重复
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查内容质量
     */
    protected function hasLowQualityContent(string $content): bool
    {
        // 检查重复字符
        if (preg_match('/(.)\1{10,}/', $content)) {
            return true;
        }
        
        // 检查是否主要由数字和符号组成
        $alphaCount = preg_match_all('/[\x{4e00}-\x{9fa5}a-zA-Z]/u', $content);
        $totalLength = mb_strlen($content, 'UTF-8');
        
        if ($totalLength > 0 && ($alphaCount / $totalLength) < 0.3) {
            return true;
        }
        
        // 检查是否包含足够的实质性内容
        $meaningfulWords = ['功能', '界面', '操作', '体验', '性能', '稳定', '流畅', '卡顿', '崩溃', '好用', '方便', '简单', '复杂', '推荐', '建议'];
        $meaningfulCount = 0;
        
        foreach ($meaningfulWords as $word) {
            if (strpos($content, $word) !== false) {
                $meaningfulCount++;
            }
        }
        
        return $meaningfulCount < 2; // 至少包含2个有意义的词汇
    }

    /**
     * 检查提交频率限制
     */
    protected function exceedsSubmissionLimit(int $userId): bool
    {
        $todayCount = EvaluationReport::where('user_id', $userId)
                                    ->whereDate('created_at', Carbon::today())
                                    ->count();
        
        return $todayCount >= 5; // 每天最多提交5个报告
    }

    /**
     * 准备验证数据
     */
    protected function prepareForValidation()
    {
        // 清理和标准化输入数据
        $this->merge([
            'report_title' => trim($this->input('report_title', '')),
            'report_content' => trim($this->input('report_content', '')),
            'task_description' => trim($this->input('task_description', '')),
            'pros' => trim($this->input('pros', '')),
            'cons' => trim($this->input('cons', '')),
            'suggestions' => trim($this->input('suggestions', ''))
        ]);
    }

    /**
     * 获取验证后的数据并进行额外处理
     */
    public function validated()
    {
        $validated = parent::validated();
        
        // 过滤空值
        $validated = array_filter($validated, function ($value) {
            return $value !== '' && $value !== null;
        });
        
        // 确保screenshots是数组
        if (!isset($validated['screenshots'])) {
            $validated['screenshots'] = [];
        }
        
        return $validated;
    }
}