<?php

return [
    /*
    |--------------------------------------------------------------------------
    | CitUI Configuration
    |--------------------------------------------------------------------------
    |
    | CitUI应用的配置选项
    |
    */

    // 是否撤销旧Token（登录时）
    'revoke_old_tokens' => env('CITUI_REVOKE_OLD_TOKENS', false),

    // 是否记录API访问日志
    'log_api_access' => env('CITUI_LOG_API_ACCESS', false),

    // Token过期时间（分钟）
    'token_expiration' => env('CITUI_TOKEN_EXPIRATION', 525600), // 默认1年

    // 短信验证码配置
    'sms' => [
        // 验证码长度
        'code_length' => 6,
        
        // 验证码有效期（分钟）
        'code_expiration' => 5,
        
        // 发送频率限制（每小时最多次数）
        'rate_limit' => 5,
        
        // 重发间隔（秒）
        'resend_interval' => 60,
        
        // 短信模板
        'templates' => [
            'register' => '您的注册验证码是：{code}，5分钟内有效。',
            'login' => '您的登录验证码是：{code}，5分钟内有效。',
            'reset_password' => '您的密码重置验证码是：{code}，5分钟内有效。',
        ],
    ],

    // 积分系统配置
    'points' => [
        // 等级阈值
        'level_thresholds' => [
            1 => 0,
            2 => 100,
            3 => 500,
            4 => 1500,
            5 => 5000,
            6 => 15000,
        ],
        
        // 等级名称
        'level_names' => [
            1 => '新手',
            2 => '初级',
            3 => '中级',
            4 => '高级',
            5 => '专家',
            6 => '大师',
        ],
    ],

    // 文件上传配置
    'upload' => [
        // 允许的文件类型
        'allowed_types' => ['jpg', 'jpeg', 'png', 'webp', 'gif'],
        
        // 最大文件大小（字节）
        'max_size' => 2 * 1024 * 1024, // 2MB
        
        // 上传路径
        'path' => 'uploads/citui',
        
        // 头像上传配置
        'avatar' => [
            'allowed_types' => ['jpg', 'jpeg', 'png', 'webp'],
            'max_size' => 1 * 1024 * 1024, // 1MB
            'path' => 'uploads/citui/avatars',
        ],
        
        // 截图上传配置
        'screenshot' => [
            'allowed_types' => ['jpg', 'jpeg', 'png', 'webp'],
            'max_size' => 5 * 1024 * 1024, // 5MB
            'path' => 'uploads/citui/screenshots',
        ],
    ],

    // 审核系统配置
    'audit' => [
        // 自动审核关键词
        'keywords' => [
            'forbidden' => ['违法', '色情', '赌博', '毒品'],
            'sensitive' => ['政治', '宗教', '暴力'],
        ],
        
        // 审核分数阈值
        'score_threshold' => [
            'auto_pass' => 80,
            'auto_reject' => 20,
        ],
    ],

    // 缓存配置
    'cache' => [
        // 缓存前缀
        'prefix' => 'citui:',
        
        // 默认缓存时间（秒）
        'default_ttl' => 3600,
        
        // APP列表缓存时间
        'app_list_ttl' => 1800,
        
        // APP详情缓存时间
        'app_detail_ttl' => 3600,
        
        // 用户信息缓存时间
        'user_info_ttl' => 1800,
    ],

    // API响应配置
    'api' => [
        // 默认分页大小
        'per_page' => 15,
        
        // 最大分页大小
        'max_per_page' => 100,
        
        // 响应格式版本
        'version' => '1.0',
    ],
];