<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class EvaluationDetail extends BaseCituiModel
{
    protected $table = 'evaluation_details';
    protected $primaryKey = 'detail_id';
    
    protected $fillable = [
        'report_id',
        'step_number',
        'step_title',
        'step_description',
        'step_screenshot',
        'step_result',
        'execution_time',
        'notes'
    ];
    
    protected $casts = [
        'report_id' => 'integer',
        'step_number' => 'integer',
        'execution_time' => 'integer'
    ];
    
    protected $appends = [
        'screenshot_full_url',
        'execution_time_text'
    ];
    
    /**
     * 关联评测报告
     */
    public function report()
    {
        return $this->belongsTo(EvaluationReport::class, 'report_id', 'report_id');
    }
    
    /**
     * 获取完整截图URL
     */
    public function getScreenshotFullUrlAttribute(): string
    {
        if (!$this->step_screenshot) {
            return '';
        }
        
        if (str_starts_with($this->step_screenshot, 'http')) {
            return $this->step_screenshot;
        }
        
        return config('app.url') . $this->step_screenshot;
    }
    
    /**
     * 获取执行时间文本
     */
    public function getExecutionTimeTextAttribute(): string
    {
        if (!$this->execution_time) {
            return '未知';
        }
        
        if ($this->execution_time < 60) {
            return $this->execution_time . '秒';
        }
        
        $minutes = intval($this->execution_time / 60);
        $seconds = $this->execution_time % 60;
        
        return $seconds > 0 ? "{$minutes}分{$seconds}秒" : "{$minutes}分钟";
    }
    
    /**
     * 查询作用域 - 按报告筛选
     */
    public function scopeByReport(Builder $query, int $reportId): Builder
    {
        return $query->where('report_id', $reportId);
    }
    
    /**
     * 查询作用域 - 按步骤排序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('step_number');
    }
    
    /**
     * 查询作用域 - 有截图的步骤
     */
    public function scopeWithScreenshot(Builder $query): Builder
    {
        return $query->whereNotNull('step_screenshot');
    }
    
    /**
     * 检查是否有截图
     */
    public function hasScreenshot(): bool
    {
        return !empty($this->step_screenshot);
    }
    
    /**
     * 检查是否有执行结果
     */
    public function hasResult(): bool
    {
        return !empty($this->step_result);
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'detail_id' => $this->detail_id,
            'report_id' => $this->report_id,
            'step_number' => $this->step_number,
            'step_title' => $this->step_title,
            'step_description' => $this->step_description,
            'step_screenshot' => $this->screenshot_full_url,
            'step_result' => $this->step_result,
            'execution_time' => $this->execution_time,
            'execution_time_text' => $this->execution_time_text,
            'notes' => $this->notes,
            'has_screenshot' => $this->hasScreenshot(),
            'has_result' => $this->hasResult(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}