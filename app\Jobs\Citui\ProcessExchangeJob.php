<?php

declare(strict_types=1);

namespace App\Jobs\Citui;

use App\Models\Citui\ExchangeRecord;
use App\Services\Citui\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessExchangeJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected ExchangeRecord $exchange;
    protected NotificationService $notificationService;

    /**
     * Create a new job instance.
     */
    public function __construct(ExchangeRecord $exchange)
    {
        $this->exchange = $exchange;
        $this->notificationService = app(NotificationService::class);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('开始处理兑换任务', [
                'exchange_id' => $this->exchange->exchange_id,
                'reward_type' => $this->exchange->rewardConfig->reward_type
            ]);

            // 根据奖励类型进行不同的处理
            switch ($this->exchange->rewardConfig->reward_type) {
                case 'cash':
                    $this->processCashReward();
                    break;
                    
                case 'coupon':
                    $this->processCouponReward();
                    break;
                    
                case 'vip':
                    $this->processVipReward();
                    break;
                    
                case 'points':
                    $this->processPointsReward();
                    break;
                    
                case 'gift':
                    $this->processGiftReward();
                    break;
                    
                case 'service':
                    $this->processServiceReward();
                    break;
                    
                default:
                    throw new \Exception('不支持的奖励类型：' . $this->exchange->rewardConfig->reward_type);
            }

            Log::info('兑换任务处理完成', [
                'exchange_id' => $this->exchange->exchange_id
            ]);

        } catch (\Exception $e) {
            Log::error('兑换任务处理失败', [
                'exchange_id' => $this->exchange->exchange_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 标记兑换为失败
            $this->exchange->markAsFailed($e->getMessage());
            
            // 发送失败通知
            $this->notificationService->sendExchangeProcessingNotification($this->exchange);
            
            throw $e;
        }
    }

    /**
     * 处理现金奖励
     */
    protected function processCashReward(): void
    {
        // 开始处理
        $this->exchange->startProcessing();
        $this->notificationService->sendExchangeProcessingNotification($this->exchange);

        // 模拟处理时间
        sleep(2);

        // 这里应该调用第三方支付接口进行转账
        // 例如微信支付、支付宝等
        $transferResult = $this->transferCash();

        if ($transferResult['success']) {
            // 更新兑换记录
            $this->exchange->complete();
            $this->exchange->updateDeliveryStatus('delivered', [
                'transfer_id' => $transferResult['transfer_id'],
                'transfer_time' => now()->format('Y-m-d H:i:s')
            ]);

            // 发送完成通知
            $this->notificationService->sendExchangeCompletedNotification($this->exchange);
        } else {
            throw new \Exception('现金转账失败：' . $transferResult['message']);
        }
    }

    /**
     * 处理优惠券奖励
     */
    protected function processCouponReward(): void
    {
        // 开始处理
        $this->exchange->startProcessing();
        $this->notificationService->sendExchangeProcessingNotification($this->exchange);

        // 模拟处理时间
        sleep(1);

        // 生成优惠券码
        $couponCode = $this->generateCouponCode();

        // 更新兑换记录
        $this->exchange->complete();
        $this->exchange->updateDeliveryStatus('delivered', [
            'coupon_code' => $couponCode,
            'expire_date' => now()->addDays(30)->format('Y-m-d H:i:s')
        ]);

        // 发送完成通知
        $this->notificationService->sendExchangeCompletedNotification($this->exchange);
    }

    /**
     * 处理VIP奖励
     */
    protected function processVipReward(): void
    {
        // 开始处理
        $this->exchange->startProcessing();
        $this->notificationService->sendExchangeProcessingNotification($this->exchange);

        // 获取VIP天数
        $vipDays = $this->getVipDays();

        // 更新用户VIP状态
        $user = $this->exchange->user;
        $currentVipEnd = $user->vip_end_at ?? now();
        $newVipEnd = max($currentVipEnd, now())->addDays($vipDays);
        
        $user->update([
            'vip_end_at' => $newVipEnd,
            'is_vip' => true
        ]);

        // 更新兑换记录
        $this->exchange->complete();
        $this->exchange->updateDeliveryStatus('delivered', [
            'vip_days' => $vipDays,
            'vip_end_at' => $newVipEnd->format('Y-m-d H:i:s')
        ]);

        // 发送完成通知
        $this->notificationService->sendExchangeCompletedNotification($this->exchange);
    }

    /**
     * 处理积分奖励
     */
    protected function processPointsReward(): void
    {
        // 开始处理
        $this->exchange->startProcessing();
        $this->notificationService->sendExchangeProcessingNotification($this->exchange);

        // 获取奖励积分数量
        $bonusPoints = (int) $this->exchange->rewardConfig->reward_value;

        // 给用户增加积分
        $user = $this->exchange->user;
        $user->addPoints($bonusPoints, 'reward_bonus', [
            'source_id' => $this->exchange->exchange_id,
            'description' => '积分奖励兑换'
        ]);

        // 更新兑换记录
        $this->exchange->complete();
        $this->exchange->updateDeliveryStatus('delivered', [
            'bonus_points' => $bonusPoints,
            'delivered_at' => now()->format('Y-m-d H:i:s')
        ]);

        // 发送完成通知
        $this->notificationService->sendExchangeCompletedNotification($this->exchange);
    }

    /**
     * 处理实物礼品
     */
    protected function processGiftReward(): void
    {
        // 开始处理
        $this->exchange->startProcessing();
        $this->notificationService->sendExchangeProcessingNotification($this->exchange);

        // 模拟处理时间
        sleep(3);

        // 创建发货单
        $shippingResult = $this->createShippingOrder();

        if ($shippingResult['success']) {
            // 更新配送状态
            $this->exchange->updateDeliveryStatus('shipped', [
                'shipping_company' => $shippingResult['shipping_company'],
                'tracking_number' => $shippingResult['tracking_number'],
                'shipped_at' => now()->format('Y-m-d H:i:s')
            ]);

            // 发送配送通知
            $this->notificationService->sendDeliveryUpdateNotification($this->exchange);

            // 完成兑换
            $this->exchange->complete();
        } else {
            throw new \Exception('创建发货单失败：' . $shippingResult['message']);
        }
    }

    /**
     * 处理服务奖励
     */
    protected function processServiceReward(): void
    {
        // 开始处理
        $this->exchange->startProcessing();
        $this->notificationService->sendExchangeProcessingNotification($this->exchange);

        // 根据服务类型进行处理
        $serviceType = $this->exchange->exchange_data['service_type'] ?? 'phone_recharge';

        switch ($serviceType) {
            case 'phone_recharge':
                $this->processPhoneRecharge();
                break;
            default:
                throw new \Exception('不支持的服务类型：' . $serviceType);
        }
    }

    /**
     * 处理话费充值
     */
    protected function processPhoneRecharge(): void
    {
        $phone = $this->exchange->exchange_data['contact_phone'] ?? '';
        $amount = $this->exchange->rewardConfig->reward_value;

        // 调用话费充值接口
        $rechargeResult = $this->rechargePhone($phone, $amount);

        if ($rechargeResult['success']) {
            // 更新兑换记录
            $this->exchange->complete();
            $this->exchange->updateDeliveryStatus('delivered', [
                'phone' => $phone,
                'amount' => $amount,
                'recharge_id' => $rechargeResult['recharge_id'],
                'recharged_at' => now()->format('Y-m-d H:i:s')
            ]);

            // 发送完成通知
            $this->notificationService->sendExchangeCompletedNotification($this->exchange);
        } else {
            throw new \Exception('话费充值失败：' . $rechargeResult['message']);
        }
    }

    /**
     * 模拟现金转账
     */
    protected function transferCash(): array
    {
        // 这里应该调用实际的支付接口
        return [
            'success' => true,
            'transfer_id' => 'TX' . time() . rand(1000, 9999),
            'message' => '转账成功'
        ];
    }

    /**
     * 生成优惠券码
     */
    protected function generateCouponCode(): string
    {
        return strtoupper(substr(md5(uniqid()), 0, 12));
    }

    /**
     * 获取VIP天数
     */
    protected function getVipDays(): int
    {
        $rewardName = $this->exchange->rewardConfig->reward_name;
        
        if (strpos($rewardName, '7天') !== false) {
            return 7;
        } elseif (strpos($rewardName, '30天') !== false) {
            return 30;
        }
        
        return 7; // 默认7天
    }

    /**
     * 创建发货单
     */
    protected function createShippingOrder(): array
    {
        // 这里应该调用实际的物流接口
        return [
            'success' => true,
            'shipping_company' => '顺丰快递',
            'tracking_number' => 'SF' . time() . rand(100000, 999999),
            'message' => '发货成功'
        ];
    }

    /**
     * 话费充值
     */
    protected function rechargePhone(string $phone, float $amount): array
    {
        // 这里应该调用实际的话费充值接口
        return [
            'success' => true,
            'recharge_id' => 'RC' . time() . rand(1000, 9999),
            'message' => '充值成功'
        ];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('兑换任务执行失败', [
            'exchange_id' => $this->exchange->exchange_id,
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}