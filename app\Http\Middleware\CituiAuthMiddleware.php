<?php
declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\Citui\User;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

class CituiAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param string|null $permission 可选的权限检查
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $permission = null)
    {
        try {
            // 获取Authorization头部的Token
            $token = $this->extractTokenFromRequest($request);
            
            if (!$token) {
                return $this->unauthorizedResponse('缺少认证Token');
            }
            
            // 验证Token并获取用户信息
            $user = $this->validateTokenAndGetUser($token);
            
            if (!$user) {
                return $this->unauthorizedResponse('Token无效或已过期');
            }
            
            // 检查用户状态
            if (!$user->isActive()) {
                return $this->unauthorizedResponse('账户已被禁用');
            }
            
            // 权限检查（如果指定了权限）
            if ($permission && !$this->checkPermission($user, $permission)) {
                return $this->forbiddenResponse('权限不足');
            }
            
            // 将用户信息注入到请求中
            $this->injectUserToRequest($request, $user);
            
            // 记录API访问日志（可选）
            $this->logApiAccess($request, $user);
            
            return $next($request);
            
        } catch (\Exception $e) {
            Log::error('CitUI认证中间件异常', [
                'error' => $e->getMessage(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->unauthorizedResponse('认证失败');
        }
    }
    
    /**
     * 从请求中提取Token
     */
    protected function extractTokenFromRequest(Request $request): ?string
    {
        // 优先从Authorization头部获取
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }
        
        // 从query参数获取（备用方案）
        $tokenFromQuery = $request->query('token');
        if ($tokenFromQuery) {
            return $tokenFromQuery;
        }
        
        // 从请求体获取（备用方案）
        $tokenFromBody = $request->input('token');
        if ($tokenFromBody) {
            return $tokenFromBody;
        }
        
        return null;
    }
    
    /**
     * 验证Token并获取用户信息
     */
    protected function validateTokenAndGetUser(string $token): ?User
    {
        try {
            // 使用Laravel Sanctum验证Token
            $accessToken = PersonalAccessToken::findToken($token);
            
            if (!$accessToken) {
                return null;
            }
            
            // 检查Token是否过期
            if ($accessToken->expires_at && $accessToken->expires_at->isPast()) {
                // 删除过期Token
                $accessToken->delete();
                return null;
            }
            
            // 获取Token关联的用户
            $tokenable = $accessToken->tokenable;
            
            // 确保是CitUI用户模型
            if (!($tokenable instanceof User)) {
                return null;
            }
            
            // 更新Token最后使用时间
            $accessToken->forceFill(['last_used_at' => now()])->save();
            
            return $tokenable;
            
        } catch (\Exception $e) {
            Log::warning('Token验证失败', [
                'error' => $e->getMessage(),
                'token' => substr($token, 0, 10) . '...'
            ]);
            
            return null;
        }
    }
    
    /**
     * 检查用户权限
     */
    protected function checkPermission(User $user, string $permission): bool
    {
        // 基础权限检查逻辑
        switch ($permission) {
            case 'admin':
                // 检查是否为管理员
                return $user->isAdmin();
                
            case 'active':
                // 检查账户是否激活
                return $user->isActive();
                
            case 'verified':
                // 检查是否已验证手机号
                return $user->isPhoneVerified();
                
            default:
                // 默认允许通过
                return true;
        }
    }
    
    /**
     * 将用户信息注入到请求中
     */
    protected function injectUserToRequest(Request $request, User $user): void
    {
        // 设置Laravel的认证用户（使用citui守卫）
        Auth::guard('citui')->setUser($user);
        
        // 在请求属性中设置CitUI用户
        $request->attributes->set('citui_user', $user);
        
        // 在请求中设置用户ID（便于后续使用）
        $request->attributes->set('citui_user_id', $user->user_id);
        
        // 设置用户信息到请求对象（兼容现有代码）
        $request->user = $user;
        $request->user_id = $user->user_id;
    }
    
    /**
     * 记录API访问日志
     */
    protected function logApiAccess(Request $request, User $user): void
    {
        // 只在需要时记录访问日志，避免过多日志
        if (config('citui.log_api_access', false)) {
            Log::info('CitUI API访问', [
                'user_id' => $user->user_id,
                'phone' => $user->phone,
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);
        }
    }
    
    /**
     * 返回未授权响应
     */
    protected function unauthorizedResponse(string $message = '未授权访问'): JsonResponse
    {
        return response()->json([
            'status' => 401,
            'code' => 0,
            'msg' => $message,
            'data' => []
        ], 401);
    }
    
    /**
     * 返回权限不足响应
     */
    protected function forbiddenResponse(string $message = '权限不足'): JsonResponse
    {
        return response()->json([
            'status' => 403,
            'code' => 0,
            'msg' => $message,
            'data' => []
        ], 403);
    }
}