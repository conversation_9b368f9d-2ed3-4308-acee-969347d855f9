<?php

declare(strict_types=1);

namespace App\Listeners\Citui;

use App\Services\Citui\PointService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PointAwardListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected PointService $pointService;

    /**
     * Create the event listener.
     */
    public function __construct(PointService $pointService)
    {
        $this->pointService = $pointService;
    }

    /**
     * Handle user registration event
     */
    public function handleUserRegistered($event): void
    {
        try {
            $this->pointService->awardPoints($event->user->user_id, 'register', [
                'source_type' => 'register',
                'description' => '用户注册奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('注册积分发放失败', [
                'user_id' => $event->user->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle user login event
     */
    public function handleUserLoggedIn($event): void
    {
        try {
            $this->pointService->awardPoints($event->user->user_id, 'login', [
                'source_type' => 'login',
                'description' => '每日登录奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('登录积分发放失败', [
                'user_id' => $event->user->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle evaluation report submitted event
     */
    public function handleEvaluationSubmitted($event): void
    {
        try {
            $this->pointService->awardPoints($event->report->user_id, 'evaluation_submit', [
                'source_type' => 'evaluation_report',
                'source_id' => $event->report->report_id,
                'description' => '提交评测报告奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('评测提交积分发放失败', [
                'user_id' => $event->report->user_id,
                'report_id' => $event->report->report_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle evaluation report approved event
     */
    public function handleEvaluationApproved($event): void
    {
        try {
            $this->pointService->awardPoints($event->report->user_id, 'evaluation_approved', [
                'source_type' => 'evaluation_report',
                'source_id' => $event->report->report_id,
                'description' => '评测报告审核通过奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('评测审核通过积分发放失败', [
                'user_id' => $event->report->user_id,
                'report_id' => $event->report->report_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle clue submitted event
     */
    public function handleClueSubmitted($event): void
    {
        try {
            $this->pointService->awardPoints($event->clue->user_id, 'clue_submit', [
                'source_type' => 'water_clue',
                'source_id' => $event->clue->clue_id,
                'description' => '发布线索奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('线索发布积分发放失败', [
                'user_id' => $event->clue->user_id,
                'clue_id' => $event->clue->clue_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle clue approved event
     */
    public function handleClueApproved($event): void
    {
        try {
            $this->pointService->awardPoints($event->clue->user_id, 'clue_approved', [
                'source_type' => 'water_clue',
                'source_id' => $event->clue->clue_id,
                'description' => '线索审核通过奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('线索审核通过积分发放失败', [
                'user_id' => $event->clue->user_id,
                'clue_id' => $event->clue->clue_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle feedback submitted event
     */
    public function handleFeedbackSubmitted($event): void
    {
        try {
            $ruleCode = $event->feedback->result === 'success' ? 'feedback_success' : 'feedback_submit';
            $description = $event->feedback->result === 'success' ? '成功反馈奖励' : '提交反馈奖励';
            
            $this->pointService->awardPoints($event->feedback->user_id, $ruleCode, [
                'source_type' => 'clue_feedback',
                'source_id' => $event->feedback->feedback_id,
                'description' => $description
            ]);
        } catch (\Exception $e) {
            Log::warning('反馈积分发放失败', [
                'user_id' => $event->feedback->user_id,
                'feedback_id' => $event->feedback->feedback_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle user invited event
     */
    public function handleUserInvited($event): void
    {
        try {
            $this->pointService->awardPoints($event->inviter->user_id, 'invite_user', [
                'source_type' => 'invite',
                'source_id' => $event->invitee->user_id,
                'description' => '邀请用户奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('邀请积分发放失败', [
                'inviter_id' => $event->inviter->user_id,
                'invitee_id' => $event->invitee->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle content liked event
     */
    public function handleContentLiked($event): void
    {
        try {
            $this->pointService->awardPoints($event->user_id, 'like_content', [
                'source_type' => $event->content_type,
                'source_id' => $event->content_id,
                'description' => '点赞内容奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('点赞积分发放失败', [
                'user_id' => $event->user_id,
                'content_type' => $event->content_type,
                'content_id' => $event->content_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle comment submitted event
     */
    public function handleCommentSubmitted($event): void
    {
        try {
            $this->pointService->awardPoints($event->comment->user_id, 'comment', [
                'source_type' => 'comment',
                'source_id' => $event->comment->comment_id,
                'description' => '发表评论奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('评论积分发放失败', [
                'user_id' => $event->comment->user_id,
                'comment_id' => $event->comment->comment_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle daily check-in event
     */
    public function handleDailyCheckIn($event): void
    {
        try {
            $this->pointService->awardPoints($event->user->user_id, 'daily_checkin', [
                'source_type' => 'checkin',
                'description' => '每日签到奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('签到积分发放失败', [
                'user_id' => $event->user->user_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle content shared event
     */
    public function handleContentShared($event): void
    {
        try {
            $this->pointService->awardPoints($event->user_id, 'share_content', [
                'source_type' => $event->content_type,
                'source_id' => $event->content_id,
                'description' => '分享内容奖励'
            ]);
        } catch (\Exception $e) {
            Log::warning('分享积分发放失败', [
                'user_id' => $event->user_id,
                'content_type' => $event->content_type,
                'content_id' => $event->content_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        $eventClass = get_class($event);
        
        switch ($eventClass) {
            case 'App\Events\Citui\UserRegistered':
                $this->handleUserRegistered($event);
                break;
                
            case 'App\Events\Citui\UserLoggedIn':
                $this->handleUserLoggedIn($event);
                break;
                
            case 'App\Events\Citui\EvaluationSubmitted':
                $this->handleEvaluationSubmitted($event);
                break;
                
            case 'App\Events\Citui\EvaluationApproved':
                $this->handleEvaluationApproved($event);
                break;
                
            case 'App\Events\Citui\ClueSubmitted':
                $this->handleClueSubmitted($event);
                break;
                
            case 'App\Events\Citui\ClueApproved':
                $this->handleClueApproved($event);
                break;
                
            case 'App\Events\Citui\FeedbackSubmitted':
                $this->handleFeedbackSubmitted($event);
                break;
                
            case 'App\Events\Citui\UserInvited':
                $this->handleUserInvited($event);
                break;
                
            case 'App\Events\Citui\ContentLiked':
                $this->handleContentLiked($event);
                break;
                
            case 'App\Events\Citui\CommentSubmitted':
                $this->handleCommentSubmitted($event);
                break;
                
            case 'App\Events\Citui\DailyCheckIn':
                $this->handleDailyCheckIn($event);
                break;
                
            case 'App\Events\Citui\ContentShared':
                $this->handleContentShared($event);
                break;
                
            default:
                Log::info('未处理的积分事件', ['event' => $eventClass]);
                break;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, $exception): void
    {
        Log::error('积分发放任务失败', [
            'event' => get_class($event),
            'exception' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}