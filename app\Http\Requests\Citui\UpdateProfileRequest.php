<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class UpdateProfileRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'nickname' => [
                'sometimes',
                'string',
                'max:50',
                'regex:/^[\x{4e00}-\x{9fa5}a-zA-Z0-9_]+$/u',
                'unique:ct_users,nickname,' . auth()->id() . ',user_id'
            ],
            'real_name' => 'sometimes|string|max:20',
            'gender' => 'sometimes|in:male,female,unknown',
            'birthday' => 'sometimes|date|before:today',
            'province' => 'sometimes|string|max:20',
            'city' => 'sometimes|string|max:20'
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'nickname.regex' => '昵称只能包含中文、英文、数字和下划线',
            'nickname.unique' => '昵称已被使用',
            'birthday.before' => '生日必须是今天之前的日期',
            'gender.in' => '性别只能是 male、female 或 unknown'
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'nickname' => '昵称',
            'real_name' => '真实姓名',
            'gender' => '性别',
            'birthday' => '生日',
            'province' => '省份',
            'city' => '城市'
        ]);
    }
}