<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class EvaluationComment extends BaseCituiModel
{
    protected $table = 'evaluation_comments';
    protected $primaryKey = 'comment_id';
    
    protected $fillable = [
        'report_id',
        'user_id',
        'parent_id',
        'comment_content',
        'status',
        'like_count'
    ];
    
    protected $casts = [
        'report_id' => 'integer',
        'user_id' => 'integer',
        'parent_id' => 'integer',
        'like_count' => 'integer'
    ];
    
    protected $appends = [
        'status_text',
        'is_reply'
    ];
    
    /**
     * 关联评测报告
     */
    public function report()
    {
        return $this->belongsTo(EvaluationReport::class, 'report_id', 'report_id');
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联父评论
     */
    public function parent()
    {
        return $this->belongsTo(static::class, 'parent_id', 'comment_id');
    }
    
    /**
     * 关联子评论（回复）
     */
    public function replies()
    {
        return $this->hasMany(static::class, 'parent_id', 'comment_id')
                    ->where('status', 'approved')
                    ->orderBy('created_at');
    }
    
    /**
     * 关联所有子评论（递归）
     */
    public function allReplies()
    {
        return $this->replies()->with('allReplies');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝'
        ];
        
        return $statuses[$this->status] ?? '未知';
    }
    
    /**
     * 检查是否为回复
     */
    public function getIsReplyAttribute(): bool
    {
        return !is_null($this->parent_id);
    }
    
    /**
     * 查询作用域 - 按报告筛选
     */
    public function scopeByReport(Builder $query, int $reportId): Builder
    {
        return $query->where('report_id', $reportId);
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 顶级评论
     */
    public function scopeTopLevel(Builder $query): Builder
    {
        return $query->whereNull('parent_id');
    }
    
    /**
     * 查询作用域 - 回复评论
     */
    public function scopeReplies(Builder $query, int $parentId): Builder
    {
        return $query->where('parent_id', $parentId);
    }
    
    /**
     * 查询作用域 - 已审核通过
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }
    
    /**
     * 查询作用域 - 待审核
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * 查询作用域 - 按点赞数排序
     */
    public function scopeByLikes(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('like_count', $direction);
    }
    
    /**
     * 审核通过
     */
    public function approve(): bool
    {
        $this->status = 'approved';
        return $this->save();
    }
    
    /**
     * 审核拒绝
     */
    public function reject(): bool
    {
        $this->status = 'rejected';
        return $this->save();
    }
    
    /**
     * 增加点赞次数
     */
    public function incrementLikeCount(): void
    {
        $this->increment('like_count');
    }
    
    /**
     * 减少点赞次数
     */
    public function decrementLikeCount(): void
    {
        $this->decrement('like_count');
    }
    
    /**
     * 检查是否已审核通过
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }
    
    /**
     * 检查是否为顶级评论
     */
    public function isTopLevel(): bool
    {
        return is_null($this->parent_id);
    }
    
    /**
     * 获取评论层级
     */
    public function getLevel(): int
    {
        $level = 1;
        $parent = $this->parent;
        
        while ($parent) {
            $level++;
            $parent = $parent->parent;
        }
        
        return $level;
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'comment_id' => $this->comment_id,
            'report_id' => $this->report_id,
            'user_id' => $this->user_id,
            'user' => $this->user?->toApiArray(),
            'parent_id' => $this->parent_id,
            'parent' => $this->parent?->toApiArray(),
            'comment_content' => $this->comment_content,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'like_count' => $this->like_count,
            'is_reply' => $this->is_reply,
            'is_top_level' => $this->isTopLevel(),
            'level' => $this->getLevel(),
            'replies_count' => $this->replies()->count(),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}