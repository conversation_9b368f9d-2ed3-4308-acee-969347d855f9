# 用户管理系统 API 文档

## 概述

用户管理系统提供了完整的用户信息管理、头像上传、密码修改和用户关系管理功能。

## API 端点

### 1. 获取用户信息

**GET** `/api/citui/user/profile`

获取当前登录用户的详细信息，包括统计数据和最近活动。

**请求头：**
```
Authorization: Bearer {token}
```

**响应示例：**
```json
{
    "status": 200,
    "code": 1,
    "msg": "获取用户信息成功",
    "data": {
        "user": {
            "user_id": 1,
            "phone": "13800138000",
            "nickname": "测试用户",
            "real_name": "张三",
            "avatar_url": "/storage/avatars/2024/01/01/avatar_1_1704067200.jpg",
            "display_name": "测试用户",
            "gender": "male",
            "birthday": "1990-01-01",
            "age": 34,
            "province": "北京市",
            "city": "朝阳区",
            "total_points": 1500,
            "available_points": 1200,
            "level": 3,
            "level_name": "中级",
            "status": "active"
        },
        "statistics": {
            "evaluation_count": 5,
            "clue_count": 3,
            "following_count": 10,
            "followers_count": 8,
            "join_days": 30
        },
        "recent_evaluations": [...],
        "recent_clues": [...]
    }
}
```

### 2. 更新用户信息

**PUT** `/api/citui/user/profile`

更新用户的基本信息。

**请求体：**
```json
{
    "nickname": "新昵称",
    "real_name": "真实姓名",
    "gender": "male",
    "birthday": "1990-01-01",
    "province": "北京市",
    "city": "朝阳区"
}
```

**验证规则：**
- `nickname`: 可选，字符串，最大50字符，只能包含中文、英文、数字和下划线，不能重复
- `real_name`: 可选，字符串，最大20字符
- `gender`: 可选，枚举值：male, female, unknown
- `birthday`: 可选，日期格式，必须是今天之前的日期
- `province`: 可选，字符串，最大20字符
- `city`: 可选，字符串，最大20字符

### 3. 上传头像

**POST** `/api/citui/user/avatar`

上传用户头像图片。

**请求体：** `multipart/form-data`
```
avatar: [图片文件]
```

**验证规则：**
- 必须是图片文件
- 支持格式：JPEG, PNG, WebP
- 文件大小不超过 2MB
- 图片尺寸：100x100 到 2000x2000 像素

**响应示例：**
```json
{
    "status": 200,
    "code": 1,
    "msg": "头像上传成功",
    "data": {
        "file_id": 123,
        "avatar_url": "/storage/avatars/2024/01/01/avatar_1_1704067200.jpg",
        "full_url": "https://example.com/storage/avatars/2024/01/01/avatar_1_1704067200.jpg",
        "file_size": "256 KB",
        "dimensions": "200x200"
    }
}
```

### 4. 修改密码

**PUT** `/api/citui/user/password`

修改用户登录密码。

**请求体：**
```json
{
    "old_password": "原密码",
    "new_password": "新密码123",
    "confirm_password": "新密码123"
}
```

**验证规则：**
- `old_password`: 必填，必须与当前密码匹配
- `new_password`: 必填，6-20位，必须包含字母和数字，不能与原密码相同
- `confirm_password`: 必填，必须与新密码一致

**注意：** 密码修改成功后，用户的所有 Token 将被清除，需要重新登录。

### 5. 获取用户关系列表

**GET** `/api/citui/user/relations`

获取用户的关注列表或粉丝列表。

**查询参数：**
- `type`: 关系类型，可选值：follow（我关注的），fans（关注我的），默认：follow
- `status`: 关系状态，可选值：accepted, pending, rejected
- `search`: 搜索关键词，搜索昵称或真实姓名
- `sort_by`: 排序字段，可选值：created_at, updated_at, nickname，默认：created_at
- `sort_order`: 排序方向，可选值：asc, desc，默认：desc
- `per_page`: 每页数量，1-50，默认：15

**响应示例：**
```json
{
    "status": 200,
    "code": 1,
    "msg": "获取用户关系列表成功",
    "data": {
        "relations": [...],
        "pagination": {
            "current_page": 1,
            "per_page": 15,
            "total": 100,
            "last_page": 7,
            "has_more": true
        },
        "statistics": {
            "total_count": 100,
            "pending_count": 5
        }
    }
}
```

### 6. 关注/取消关注用户

**POST** `/api/citui/user/relations/{userId}/follow`

关注或取消关注指定用户。

**路径参数：**
- `userId`: 目标用户ID

**请求体：**
```json
{
    "action": "follow"
}
```

**验证规则：**
- `action`: 必填，可选值：follow（关注），unfollow（取消关注）

**响应示例：**
```json
{
    "status": 200,
    "code": 1,
    "msg": "关注成功",
    "data": {
        "action": "followed",
        "user_id": 1,
        "target_user_id": 2,
        "is_following": true,
        "is_mutual": false,
        "target_user": {
            "user_id": 2,
            "nickname": "目标用户",
            "avatar_url": "/storage/avatars/default.jpg",
            "level": 2,
            "level_name": "初级"
        }
    }
}
```

### 7. 获取关注状态

**GET** `/api/citui/user/relations/{userId}/status`

获取与指定用户的关注状态。

**响应示例：**
```json
{
    "status": 200,
    "code": 1,
    "msg": "获取关注状态成功",
    "data": {
        "is_following": true,
        "is_followed": false,
        "is_mutual": false
    }
}
```

### 8. 查看其他用户信息

**GET** `/api/citui/users/{userId}`

查看其他用户的公开信息。

**响应示例：**
```json
{
    "status": 200,
    "code": 1,
    "msg": "获取用户信息成功",
    "data": {
        "user": {
            "user_id": 2,
            "nickname": "其他用户",
            "avatar_url": "/storage/avatars/default.jpg",
            "display_name": "其他用户",
            "level": 2,
            "level_name": "初级",
            "statistics": {
                "evaluation_count": 3,
                "clue_count": 1,
                "level": 2,
                "level_name": "初级",
                "join_days": 15
            }
        },
        "follow_status": {
            "is_following": true,
            "is_followed": false,
            "is_mutual": false
        },
        "recent_evaluations": [...],
        "recent_clues": [...]
    }
}
```

## 错误响应

所有 API 在出错时都会返回统一的错误格式：

```json
{
    "status": 400,
    "code": 0,
    "msg": "错误信息",
    "data": null
}
```

常见错误码：
- `401`: 用户未登录
- `400`: 请求参数错误
- `422`: 数据验证失败
- `404`: 资源不存在
- `500`: 服务器内部错误

## 认证

所有需要认证的接口都需要在请求头中包含有效的 Bearer Token：

```
Authorization: Bearer {your_access_token}
```

Token 可以通过登录接口获取，详见认证系统文档。