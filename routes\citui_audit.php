<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Citui\AuditController;

/*
|--------------------------------------------------------------------------
| CitUI 审核系统路由
|--------------------------------------------------------------------------
|
| 这里定义了 CitUI 审核系统的所有 API 路由
|
*/

Route::prefix('citui/audit')->middleware(['auth:sanctum'])->group(function () {
    
    // 管理员审核相关路由
    Route::middleware(['admin'])->group(function () {
        // 获取审核列表
        Route::get('/', [AuditController::class, 'index']);
        
        // 获取审核详情
        Route::get('/{auditId}', [AuditController::class, 'show']);
        
        // 处理单个审核
        Route::post('/{auditId}/process', [AuditController::class, 'process']);
        
        // 批量审核
        Route::post('/batch-process', [AuditController::class, 'batchProcess']);
        
        // 获取审核统计
        Route::get('/statistics/overview', [AuditController::class, 'statistics']);
        
        // 处理超时审核
        Route::post('/handle-timeouts', [AuditController::class, 'handleTimeouts']);
    });
    
    // 用户提交审核（内部使用）
    Route::post('/submit', [AuditController::class, 'submit']);
});

// 定时任务路由（可选，用于手动触发）
Route::get('/citui/audit/cron/handle-timeouts', function () {
    $auditService = app(\App\Services\Citui\AuditService::class);
    $count = $auditService->handleTimeoutAudits();
    
    return response()->json([
        'status' => 200,
        'message' => "处理了 {$count} 个超时审核",
        'count' => $count
    ]);
})->name('citui.audit.cron.timeouts');