<?php

declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class WaterClue extends BaseCituiModel
{
    protected $table = 'water_clues';
    protected $primaryKey = 'clue_id';

    protected $fillable = [
        'app_id',
        'user_id',
        'clue_title',
        'clue_content',
        'clue_type',
        'difficulty_level',
        'expected_reward',
        'actual_reward',
        'success_rate',
        'risk_level',
        'steps',
        'screenshots',
        'tags',
        'reward_points',
        'status',
        'view_count',
        'like_count',
        'try_count',
        'success_count',
        'is_featured',
        'expires_at',
        'submitted_at',
        'approved_at'
    ];

    protected $casts = [
        'app_id' => 'integer',
        'user_id' => 'integer',
        'expected_reward' => 'decimal:2',
        'actual_reward' => 'decimal:2',
        'success_rate' => 'decimal:2',
        'steps' => 'array',
        'screenshots' => 'array',
        'reward_points' => 'integer',
        'view_count' => 'integer',
        'like_count' => 'integer',
        'try_count' => 'integer',
        'success_count' => 'integer',
        'is_featured' => 'boolean',
        'expires_at' => 'datetime',
        'submitted_at' => 'datetime',
        'approved_at' => 'datetime'
    ];

    protected $appends = [
        'screenshots_full_urls',
        'tags_array',
        'status_text',
        'clue_type_text',
        'difficulty_text',
        'risk_level_text',
        'is_expired',
        'days_until_expiry'
    ];

    /**
     * 关联APP
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'app_id', 'app_id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    /**
     * 关联反馈
     */
    public function feedbacks()
    {
        return $this->hasMany(ClueFeedback::class, 'clue_id', 'clue_id');
    }

    /**
     * 关联统计数据
     */
    public function statistics()
    {
        return $this->hasMany(ClueStatistic::class, 'clue_id', 'clue_id');
    }

    /**
     * 关联点赞记录
     */
    public function likes()
    {
        return $this->hasMany(ClueLike::class, 'clue_id', 'clue_id');
    }

    /**
     * 查询作用域 - 已审核通过
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('status', 'approved');
    }

    /**
     * 查询作用域 - 推荐线索
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('is_featured', true);
    }

    /**
     * 查询作用域 - 未过期
     */
    public function scopeNotExpired(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
                ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * 查询作用域 - 按类型筛选
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('clue_type', $type);
    }

    /**
     * 查询作用域 - 按难度筛选
     */
    public function scopeByDifficulty(Builder $query, string $difficulty): Builder
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * 查询作用域 - 按风险等级筛选
     */
    public function scopeByRiskLevel(Builder $query, string $riskLevel): Builder
    {
        return $query->where('risk_level', $riskLevel);
    }

    /**
     * 查询作用域 - 按成功率排序
     */
    public function scopeBySuccessRate(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('success_rate', $direction);
    }



    /**
     * 获取完整截图URLs
     */
    public function getScreenshotsFullUrlsAttribute(): array
    {
        if (!$this->screenshots || !is_array($this->screenshots)) {
            return [];
        }

        return array_map(function ($screenshot) {
            if (str_starts_with($screenshot, 'http')) {
                return $screenshot;
            }
            return config('app.url') . $screenshot;
        }, $this->screenshots);
    }

    /**
     * 获取标签数组
     */
    public function getTagsArrayAttribute(): array
    {
        return $this->tags ? explode(',', $this->tags) : [];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'draft' => '草稿',
            'submitted' => '已提交',
            'approved' => '已通过',
            'rejected' => '已拒绝',
            'expired' => '已过期'
        ];

        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取线索类型文本
     */
    public function getClueTypeTextAttribute(): string
    {
        $types = [
            'task' => '任务线索',
            'bug' => 'Bug线索',
            'feature' => '功能线索',
            'reward' => '奖励线索',
            'other' => '其他线索'
        ];

        return $types[$this->clue_type] ?? '未知';
    }

    /**
     * 获取难度文本
     */
    public function getDifficultyTextAttribute(): string
    {
        $difficulties = [
            'easy' => '简单',
            'medium' => '中等',
            'hard' => '困难',
            'expert' => '专家'
        ];

        return $difficulties[$this->difficulty_level] ?? '未知';
    }

    /**
     * 获取风险等级文本
     */
    public function getRiskLevelTextAttribute(): string
    {
        $risks = [
            'low' => '低风险',
            'medium' => '中等风险',
            'high' => '高风险',
            'extreme' => '极高风险'
        ];

        return $risks[$this->risk_level] ?? '未知';
    }

    /**
     * 检查是否已过期
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * 获取距离过期天数
     */
    public function getDaysUntilExpiryAttribute(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        return now()->diffInDays($this->expires_at, false);
    }

    /**
     * 查询作用域 - 按APP筛选
     */
    public function scopeByApp(Builder $query, int $appId): Builder
    {
        return $query->where('app_id', $appId);
    }

    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 查询作用域 - 按标签筛选
     */
    public function scopeByTag(Builder $query, string $tag): Builder
    {
        return $query->where('tags', 'like', "%{$tag}%");
    }

    /**
     * 查询作用域 - 搜索线索
     */
    public function scopeSearch(Builder $query, string $keyword): Builder
    {
        return $query->where(function ($q) use ($keyword) {
            $q->where('clue_title', 'like', "%{$keyword}%")
                ->orWhere('clue_content', 'like', "%{$keyword}%")
                ->orWhere('tags', 'like', "%{$keyword}%");
        });
    }

    /**
     * 查询作用域 - 热门线索
     */
    public function scopePopular(Builder $query): Builder
    {
        return $query->selectRaw('*, (view_count * 0.3 + try_count * 0.4 + success_count * 0.3) as popularity_score')
            ->orderBy('popularity_score', 'desc');
    }

    /**
     * 查询作用域 - 按奖励范围筛选
     */
    public function scopeByRewardRange(Builder $query, float $minReward, float $maxReward): Builder
    {
        return $query->whereBetween('expected_reward', [$minReward, $maxReward]);
    }

    /**
     * 增加查看次数
     */
    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    /**
     * 增加尝试次数
     */
    public function incrementTryCount(): void
    {
        $this->increment('try_count');
        $this->updateSuccessRate();
    }

    /**
     * 增加成功次数
     */
    public function incrementSuccessCount(): void
    {
        $this->increment('success_count');
        $this->updateSuccessRate();
    }

    /**
     * 更新成功率
     */
    public function updateSuccessRate(): void
    {
        if ($this->try_count > 0) {
            $this->success_rate = round(($this->success_count / $this->try_count) * 100, 2);
            $this->save();
        }
    }

    /**
     * 审核通过
     */
    public function approve(): bool
    {
        $this->status = 'approved';
        $this->approved_at = now();
        return $this->save();
    }

    /**
     * 审核拒绝
     */
    public function reject(): bool
    {
        $this->status = 'rejected';
        return $this->save();
    }

    /**
     * 设为推荐
     */
    public function setFeatured(bool $featured = true): bool
    {
        $this->is_featured = $featured;
        return $this->save();
    }

    /**
     * 设置过期时间
     */
    public function setExpiry(\DateTime $expiryDate): bool
    {
        $this->expires_at = $expiryDate;
        return $this->save();
    }
    
    /**
     * 更新审核状态
     */
    public function updateAuditStatus(string $status, ?string $reason = null): bool
    {
        $statusMap = [
            'published' => 'approved',
            'rejected' => 'rejected'
        ];
        
        $this->status = $statusMap[$status] ?? $status;
        
        if ($status === 'published') {
            $this->approved_at = now();
        } elseif ($status === 'rejected' && $reason) {
            $this->reject_reason = $reason;
        }
        
        return $this->save();
    }

    /**
     * 检查是否已过期
     */
    public function isExpired(): bool
    {
        return $this->is_expired;
    }

    /**
     * 检查是否为推荐线索
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }



    /**
     * 检查是否已审核通过
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * 获取线索统计数据
     */
    public function getClueStatistics(): array
    {
        return [
            'view_count' => $this->view_count,
            'like_count' => $this->like_count,
            'try_count' => $this->try_count,
            'success_count' => $this->success_count,
            'success_rate' => $this->success_rate,
            'feedback_count' => $this->feedbacks()->count(),
            'expected_reward' => $this->expected_reward,
            'actual_reward' => $this->actual_reward
        ];
    }

    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'clue_id' => $this->clue_id,
            'app_id' => $this->app_id,
            'app' => $this->app?->toApiArray(),
            'user_id' => $this->user_id,
            'user' => $this->user?->toApiArray(),
            'clue_title' => $this->clue_title,
            'clue_content' => $this->clue_content,
            'clue_type' => $this->clue_type,
            'clue_type_text' => $this->clue_type_text,
            'difficulty_level' => $this->difficulty_level,
            'difficulty_text' => $this->difficulty_text,
            'expected_reward' => $this->expected_reward,
            'actual_reward' => $this->actual_reward,
            'success_rate' => $this->success_rate,
            'risk_level' => $this->risk_level,
            'risk_level_text' => $this->risk_level_text,
            'steps' => $this->steps,
            'screenshots' => $this->screenshots_full_urls,
            'tags' => $this->tags,
            'tags_array' => $this->tags_array,
            'reward_points' => $this->reward_points,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'view_count' => $this->view_count,
            'like_count' => $this->like_count,
            'try_count' => $this->try_count,
            'success_count' => $this->success_count,
            'is_featured' => $this->is_featured,
            'is_expired' => $this->is_expired,
            'days_until_expiry' => $this->days_until_expiry,
            'statistics' => $this->getClueStatistics(),
            'expires_at' => $this->expires_at?->format('Y-m-d H:i:s'),
            'submitted_at' => $this->submitted_at?->format('Y-m-d H:i:s'),
            'approved_at' => $this->approved_at?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}
