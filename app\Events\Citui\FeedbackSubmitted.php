<?php

declare(strict_types=1);

namespace App\Events\Citui;

use App\Models\Citui\ClueFeedback;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FeedbackSubmitted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ClueFeedback $feedback;

    /**
     * Create a new event instance.
     */
    public function __construct(ClueFeedback $feedback)
    {
        $this->feedback = $feedback;
    }
}