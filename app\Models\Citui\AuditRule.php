<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class AuditRule extends BaseCituiModel
{
    protected $table = 'audit_rules';
    protected $primaryKey = 'rule_id';
    
    protected $fillable = [
        'rule_name',
        'rule_code',
        'content_type',
        'rule_description',
        'auto_audit_enabled',
        'auto_audit_keywords',
        'manual_audit_required',
        'audit_timeout_hours',
        'pass_score_threshold',
        'reject_score_threshold',
        'reward_points',
        'penalty_points',
        'is_active',
        'sort_order',
        'created_by'
    ];
    
    protected $casts = [
        'auto_audit_enabled' => 'boolean',
        'manual_audit_required' => 'boolean',
        'audit_timeout_hours' => 'integer',
        'pass_score_threshold' => 'integer',
        'reject_score_threshold' => 'integer',
        'reward_points' => 'integer',
        'penalty_points' => 'integer',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_by' => 'integer'
    ];
    
    /**
     * 关联内容审核
     */
    public function audits()
    {
        return $this->hasMany(ContentAudit::class, 'rule_id', 'rule_id');
    }
    
    /**
     * 查询作用域 - 按内容类型筛选
     */
    public function scopeByContentType(Builder $query, string $contentType): Builder
    {
        return $query->where('content_type', $contentType);
    }
    
    /**
     * 查询作用域 - 启用自动审核
     */
    public function scopeAutoAuditEnabled(Builder $query): Builder
    {
        return $query->where('auto_audit_enabled', true);
    }
    
    /**
     * 查询作用域 - 需要人工审核
     */
    public function scopeManualAuditRequired(Builder $query): Builder
    {
        return $query->where('manual_audit_required', true);
    }
    
    /**
     * 获取自动审核关键词数组
     */
    public function getKeywordsArrayAttribute(): array
    {
        if (!$this->auto_audit_keywords) {
            return [];
        }
        
        $keywords = json_decode($this->auto_audit_keywords, true);
        return is_array($keywords) ? $keywords : [];
    }
    
    /**
     * 检查内容是否包含敏感关键词
     */
    public function containsSensitiveKeywords(string $content): bool
    {
        $keywords = $this->keywords_array;
        
        foreach ($keywords as $keyword) {
            if (stripos($content, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }
}