<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class PointRule extends BaseCituiModel
{
    protected $table = 'point_rules';
    protected $primaryKey = 'rule_id';
    
    protected $fillable = [
        'rule_name',
        'rule_code',
        'action_type',
        'points',
        'max_daily_points',
        'max_total_points',
        'conditions',
        'description',
        'is_active',
        'priority',
        'valid_from',
        'valid_until'
    ];
    
    protected $casts = [
        'points' => 'integer',
        'max_daily_points' => 'integer',
        'max_total_points' => 'integer',
        'conditions' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime'
    ];
    
    protected $appends = [
        'is_valid',
        'action_type_text'
    ];
    
    /**
     * 关联积分记录
     */
    public function pointRecords()
    {
        return $this->hasMany(PointRecord::class, 'rule_id', 'rule_id');
    }
    
    /**
     * 获取动作类型文本
     */
    public function getActionTypeTextAttribute(): string
    {
        $types = [
            'register' => '用户注册',
            'login' => '每日登录',
            'evaluation_submit' => '提交评测',
            'evaluation_approved' => '评测通过',
            'clue_submit' => '提交线索',
            'clue_approved' => '线索通过',
            'feedback_submit' => '提交反馈',
            'invite_user' => '邀请用户',
            'share_content' => '分享内容',
            'comment' => '发表评论',
            'like' => '点赞内容',
            'daily_checkin' => '每日签到'
        ];
        
        return $types[$this->action_type] ?? '未知动作';
    }
    
    /**
     * 检查规则是否有效
     */
    public function getIsValidAttribute(): bool
    {
        if (!$this->is_active) {
            return false;
        }
        
        $now = now();
        
        if ($this->valid_from && $now->lt($this->valid_from)) {
            return false;
        }
        
        if ($this->valid_until && $now->gt($this->valid_until)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 查询作用域 - 活跃规则
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
    
    /**
     * 查询作用域 - 有效规则
     */
    public function scopeValid(Builder $query): Builder
    {
        $now = now();
        return $query->where('is_active', true)
                    ->where(function ($q) use ($now) {
                        $q->whereNull('valid_from')
                          ->orWhere('valid_from', '<=', $now);
                    })
                    ->where(function ($q) use ($now) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>=', $now);
                    });
    }
    
    /**
     * 查询作用域 - 按动作类型筛选
     */
    public function scopeByActionType(Builder $query, string $actionType): Builder
    {
        return $query->where('action_type', $actionType);
    }
    
    /**
     * 查询作用域 - 按规则代码筛选
     */
    public function scopeByCode(Builder $query, string $code): Builder
    {
        return $query->where('rule_code', $code);
    }
    
    /**
     * 查询作用域 - 按优先级排序
     */
    public function scopeByPriority(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('priority', $direction);
    }
    
    /**
     * 检查用户今日是否已达到积分上限
     */
    public function checkDailyLimit(int $userId): bool
    {
        if (!$this->max_daily_points) {
            return false; // 无限制
        }
        
        $todayPoints = $this->pointRecords()
                           ->where('user_id', $userId)
                           ->whereDate('created_at', today())
                           ->sum('points');
        
        return $todayPoints >= $this->max_daily_points;
    }
    
    /**
     * 检查用户总积分是否已达到上限
     */
    public function checkTotalLimit(int $userId): bool
    {
        if (!$this->max_total_points) {
            return false; // 无限制
        }
        
        $totalPoints = $this->pointRecords()
                           ->where('user_id', $userId)
                           ->sum('points');
        
        return $totalPoints >= $this->max_total_points;
    }
    
    /**
     * 检查条件是否满足
     */
    public function checkConditions(array $context = []): bool
    {
        if (!$this->conditions || empty($this->conditions)) {
            return true;
        }
        
        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $context)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 评估单个条件
     */
    protected function evaluateCondition(array $condition, array $context): bool
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';
        
        if (!isset($context[$field])) {
            return false;
        }
        
        $contextValue = $context[$field];
        
        switch ($operator) {
            case '=':
            case '==':
                return $contextValue == $value;
            case '!=':
                return $contextValue != $value;
            case '>':
                return $contextValue > $value;
            case '>=':
                return $contextValue >= $value;
            case '<':
                return $contextValue < $value;
            case '<=':
                return $contextValue <= $value;
            case 'in':
                return in_array($contextValue, (array) $value);
            case 'not_in':
                return !in_array($contextValue, (array) $value);
            default:
                return false;
        }
    }
    
    /**
     * 计算应获得的积分
     */
    public function calculatePoints(array $context = []): int
    {
        if (!$this->is_valid || !$this->checkConditions($context)) {
            return 0;
        }
        
        // 基础积分
        $points = $this->points;
        
        // 可以根据条件动态调整积分
        if (isset($context['multiplier'])) {
            $points = intval($points * $context['multiplier']);
        }
        
        return max(0, $points);
    }
    
    /**
     * 激活规则
     */
    public function activate(): bool
    {
        $this->is_active = true;
        return $this->save();
    }
    
    /**
     * 停用规则
     */
    public function deactivate(): bool
    {
        $this->is_active = false;
        return $this->save();
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'rule_id' => $this->rule_id,
            'rule_name' => $this->rule_name,
            'rule_code' => $this->rule_code,
            'action_type' => $this->action_type,
            'action_type_text' => $this->action_type_text,
            'points' => $this->points,
            'max_daily_points' => $this->max_daily_points,
            'max_total_points' => $this->max_total_points,
            'conditions' => $this->conditions,
            'description' => $this->description,
            'is_active' => $this->is_active,
            'is_valid' => $this->is_valid,
            'priority' => $this->priority,
            'valid_from' => $this->valid_from?->format('Y-m-d H:i:s'),
            'valid_until' => $this->valid_until?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}