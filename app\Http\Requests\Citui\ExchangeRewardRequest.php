<?php

declare(strict_types=1);

namespace App\Http\Requests\Citui;

use Illuminate\Foundation\Http\FormRequest;

class ExchangeRewardRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'config_id' => [
                'required',
                'integer',
                'min:1',
                'exists:ct_reward_configs,config_id'
            ],
            'exchange_data' => [
                'sometimes',
                'array'
            ],
            'exchange_data.delivery_address' => [
                'sometimes',
                'string',
                'max:500'
            ],
            'exchange_data.contact_phone' => [
                'sometimes',
                'string',
                'regex:/^1[3-9]\d{9}$/'
            ],
            'exchange_data.contact_name' => [
                'sometimes',
                'string',
                'max:50'
            ],
            'exchange_data.remarks' => [
                'sometimes',
                'string',
                'max:200'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'config_id.required' => '奖励配置ID不能为空',
            'config_id.integer' => '奖励配置ID必须为整数',
            'config_id.min' => '奖励配置ID必须大于0',
            'config_id.exists' => '奖励配置不存在',
            'exchange_data.array' => '兑换数据格式错误',
            'exchange_data.delivery_address.string' => '收货地址必须为字符串',
            'exchange_data.delivery_address.max' => '收货地址不能超过500个字符',
            'exchange_data.contact_phone.string' => '联系电话必须为字符串',
            'exchange_data.contact_phone.regex' => '联系电话格式不正确',
            'exchange_data.contact_name.string' => '联系人姓名必须为字符串',
            'exchange_data.contact_name.max' => '联系人姓名不能超过50个字符',
            'exchange_data.remarks.string' => '备注必须为字符串',
            'exchange_data.remarks.max' => '备注不能超过200个字符'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'config_id' => '奖励配置',
            'exchange_data' => '兑换数据',
            'exchange_data.delivery_address' => '收货地址',
            'exchange_data.contact_phone' => '联系电话',
            'exchange_data.contact_name' => '联系人姓名',
            'exchange_data.remarks' => '备注'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // 确保 config_id 是整数
        if ($this->has('config_id')) {
            $this->merge([
                'config_id' => (int) $this->input('config_id')
            ]);
        }

        // 清理兑换数据中的空值
        if ($this->has('exchange_data') && is_array($this->input('exchange_data'))) {
            $exchangeData = array_filter($this->input('exchange_data'), function ($value) {
                return $value !== null && $value !== '';
            });
            
            $this->merge([
                'exchange_data' => $exchangeData
            ]);
        }
    }
}