<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;

class SearchHistory extends BaseCituiModel
{
    protected $table = 'search_histories';
    protected $primaryKey = 'history_id';
    
    protected $fillable = [
        'user_id',
        'keyword',
        'search_type',
        'result_count',
        'ip_address',
        'user_agent'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'result_count' => 'integer'
    ];
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 查询作用域 - 按搜索类型筛选
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('search_type', $type);
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 最近搜索
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
    
    /**
     * 记录搜索历史
     */
    public static function recordSearch(
        ?int $userId,
        string $keyword,
        string $searchType = 'app',
        int $resultCount = 0,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return static::create([
            'user_id' => $userId,
            'keyword' => trim($keyword),
            'search_type' => $searchType,
            'result_count' => $resultCount,
            'ip_address' => $ipAddress ?: request()->ip(),
            'user_agent' => $userAgent ?: request()->userAgent()
        ]);
    }
    
    /**
     * 获取用户搜索历史
     */
    public static function getUserHistory(int $userId, int $limit = 10): array
    {
        return static::byUser($userId)
            ->byType('app')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->pluck('keyword')
            ->unique()
            ->values()
            ->toArray();
    }
    
    /**
     * 获取热门搜索关键词
     */
    public static function getHotKeywords(int $limit = 10, int $days = 7): array
    {
        return static::byType('app')
            ->recent($days)
            ->selectRaw('keyword, COUNT(*) as search_count')
            ->groupBy('keyword')
            ->orderBy('search_count', 'desc')
            ->limit($limit)
            ->pluck('keyword')
            ->toArray();
    }
    
    /**
     * 清理过期搜索历史
     */
    public static function cleanupOldRecords(int $days = 90): int
    {
        return static::where('created_at', '<', now()->subDays($days))->delete();
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'history_id' => $this->history_id,
            'keyword' => $this->keyword,
            'search_type' => $this->search_type,
            'result_count' => $this->result_count,
            'created_at' => $this->created_at->format('Y-m-d H:i:s')
        ];
    }
}