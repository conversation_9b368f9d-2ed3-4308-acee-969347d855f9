<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ExchangeRecord extends BaseCituiModel
{
    protected $table = 'exchange_records';
    protected $primaryKey = 'exchange_id';
    
    protected $fillable = [
        'user_id',
        'reward_config_id',
        'point_record_id',
        'exchange_code',
        'required_points',
        'exchange_data',
        'status',
        'delivery_status',
        'delivery_info',
        'processed_at',
        'delivered_at',
        'remarks'
    ];
    
    protected $casts = [
        'user_id' => 'integer',
        'reward_config_id' => 'integer',
        'point_record_id' => 'integer',
        'required_points' => 'integer',
        'exchange_data' => 'array',
        'delivery_info' => 'array',
        'processed_at' => 'datetime',
        'delivered_at' => 'datetime'
    ];
    
    protected $appends = [
        'status_text',
        'delivery_status_text',
        'can_cancel',
        'days_since_exchange'
    ];
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
    
    /**
     * 关联奖励配置
     */
    public function rewardConfig()
    {
        return $this->belongsTo(RewardConfig::class, 'reward_config_id', 'config_id');
    }
    
    /**
     * 关联积分记录
     */
    public function pointRecord()
    {
        return $this->belongsTo(PointRecord::class, 'point_record_id', 'record_id');
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        $statuses = [
            'pending' => '待处理',
            'processing' => '处理中',
            'completed' => '已完成',
            'cancelled' => '已取消',
            'failed' => '处理失败'
        ];
        
        return $statuses[$this->status] ?? '未知状态';
    }
    
    /**
     * 获取配送状态文本
     */
    public function getDeliveryStatusTextAttribute(): string
    {
        $statuses = [
            'none' => '无需配送',
            'pending' => '待发货',
            'shipped' => '已发货',
            'delivered' => '已送达',
            'failed' => '配送失败'
        ];
        
        return $statuses[$this->delivery_status] ?? '未知状态';
    }
    
    /**
     * 检查是否可以取消
     */
    public function getCanCancelAttribute(): bool
    {
        return in_array($this->status, ['pending', 'processing']) && 
               $this->delivery_status === 'none';
    }
    
    /**
     * 获取兑换天数
     */
    public function getDaysSinceExchangeAttribute(): int
    {
        return $this->created_at->diffInDays(now());
    }
    
    /**
     * 查询作用域 - 按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
    
    /**
     * 查询作用域 - 按状态筛选
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }
    
    /**
     * 查询作用域 - 按配送状态筛选
     */
    public function scopeByDeliveryStatus(Builder $query, string $deliveryStatus): Builder
    {
        return $query->where('delivery_status', $deliveryStatus);
    }
    
    /**
     * 查询作用域 - 待处理的兑换
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }
    
    /**
     * 查询作用域 - 处理中的兑换
     */
    public function scopeProcessing(Builder $query): Builder
    {
        return $query->where('status', 'processing');
    }
    
    /**
     * 查询作用域 - 已完成的兑换
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }
    
    /**
     * 查询作用域 - 可取消的兑换
     */
    public function scopeCancellable(Builder $query): Builder
    {
        return $query->whereIn('status', ['pending', 'processing'])
                    ->where('delivery_status', 'none');
    }
    
    /**
     * 查询作用域 - 今天的兑换
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('created_at', Carbon::today());
    }
    
    /**
     * 查询作用域 - 本月的兑换
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
                    ->whereYear('created_at', Carbon::now()->year);
    }
    
    /**
     * 生成兑换码
     */
    public static function generateExchangeCode(): string
    {
        do {
            $code = 'EX' . date('Ymd') . strtoupper(substr(md5(uniqid()), 0, 6));
        } while (static::where('exchange_code', $code)->exists());
        
        return $code;
    }
    
    /**
     * 开始处理
     */
    public function startProcessing(): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }
        
        $this->status = 'processing';
        $this->processed_at = now();
        return $this->save();
    }
    
    /**
     * 完成处理
     */
    public function complete(): bool
    {
        if (!in_array($this->status, ['pending', 'processing'])) {
            return false;
        }
        
        $this->status = 'completed';
        if (!$this->processed_at) {
            $this->processed_at = now();
        }
        return $this->save();
    }
    
    /**
     * 取消兑换
     */
    public function cancel(string $reason = ''): bool
    {
        if (!$this->can_cancel) {
            return false;
        }
        
        $this->status = 'cancelled';
        if ($reason) {
            $this->remarks = $reason;
        }
        return $this->save();
    }
    
    /**
     * 标记为失败
     */
    public function markAsFailed(string $reason = ''): bool
    {
        $this->status = 'failed';
        if ($reason) {
            $this->remarks = $reason;
        }
        return $this->save();
    }
    
    /**
     * 更新配送状态
     */
    public function updateDeliveryStatus(string $status, array $info = []): bool
    {
        $this->delivery_status = $status;
        
        if (!empty($info)) {
            $currentInfo = $this->delivery_info ?? [];
            $this->delivery_info = array_merge($currentInfo, $info);
        }
        
        if ($status === 'delivered') {
            $this->delivered_at = now();
        }
        
        return $this->save();
    }
    
    /**
     * 获取兑换统计
     */
    public static function getExchangeStats(int $userId, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);
        
        $records = static::byUser($userId)
                        ->where('created_at', '>=', $startDate)
                        ->get();
        
        return [
            'total_exchanges' => $records->count(),
            'total_points_spent' => $records->sum('required_points'),
            'completed_exchanges' => $records->where('status', 'completed')->count(),
            'pending_exchanges' => $records->where('status', 'pending')->count(),
            'cancelled_exchanges' => $records->where('status', 'cancelled')->count(),
            'success_rate' => $records->count() > 0 ? 
                round(($records->where('status', 'completed')->count() / $records->count()) * 100, 2) : 0,
            'period_days' => $days
        ];
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'exchange_id' => $this->exchange_id,
            'user_id' => $this->user_id,
            'user' => $this->user?->toApiArray(),
            'reward_config_id' => $this->reward_config_id,
            'reward_config' => $this->rewardConfig?->toApiArray(),
            'point_record_id' => $this->point_record_id,
            'point_record' => $this->pointRecord?->toApiArray(),
            'exchange_code' => $this->exchange_code,
            'required_points' => $this->required_points,
            'exchange_data' => $this->exchange_data,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'delivery_status' => $this->delivery_status,
            'delivery_status_text' => $this->delivery_status_text,
            'delivery_info' => $this->delivery_info,
            'can_cancel' => $this->can_cancel,
            'days_since_exchange' => $this->days_since_exchange,
            'processed_at' => $this->processed_at?->format('Y-m-d H:i:s'),
            'delivered_at' => $this->delivered_at?->format('Y-m-d H:i:s'),
            'remarks' => $this->remarks,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}