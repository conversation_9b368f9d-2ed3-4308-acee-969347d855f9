<?php
declare(strict_types=1);

namespace App\Console\Commands\Citui;

use Illuminate\Console\Command;
use App\Models\Citui\File;
use App\Services\Citui\FileSecurityValidator;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ScanFiles extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'citui:scan-files 
                            {--type=security : 扫描类型 (security|integrity|all)}
                            {--batch-size=100 : 批处理大小}
                            {--quarantine : 隔离可疑文件}';

    /**
     * 命令描述
     */
    protected $description = '扫描CitUI文件系统中的文件安全性和完整性';

    protected FileSecurityValidator $securityValidator;

    public function __construct()
    {
        parent::__construct();
        $this->securityValidator = new FileSecurityValidator();
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $batchSize = (int) $this->option('batch-size');
        $quarantine = $this->option('quarantine');

        $this->info('开始扫描CitUI文件系统...');

        switch ($type) {
            case 'security':
                return $this->scanSecurity($batchSize, $quarantine);

            case 'integrity':
                return $this->scanIntegrity($batchSize);

            case 'all':
            default:
                $this->info('执行安全扫描...');
                $securityResult = $this->scanSecurity($batchSize, $quarantine);
                
                $this->info('执行完整性扫描...');
                $integrityResult = $this->scanIntegrity($batchSize);
                
                return max($securityResult, $integrityResult);
        }
    }

    /**
     * 安全扫描
     */
    protected function scanSecurity(int $batchSize, bool $quarantine): int
    {
        $this->info('正在执行安全扫描...');
        
        $totalFiles = File::where('is_deleted', false)->count();
        $this->info("总共需要扫描 {$totalFiles} 个文件");

        $scannedCount = 0;
        $suspiciousCount = 0;
        $quarantinedCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($totalFiles);
        $progressBar->start();

        File::where('is_deleted', false)
            ->chunk($batchSize, function ($files) use (&$scannedCount, &$suspiciousCount, &$quarantinedCount, &$errorCount, $quarantine, $progressBar) {
                foreach ($files as $file) {
                    try {
                        $result = $this->scanFileForSecurity($file, $quarantine);
                        
                        if ($result['suspicious']) {
                            $suspiciousCount++;
                            if ($result['quarantined']) {
                                $quarantinedCount++;
                            }
                        }
                        
                        $scannedCount++;
                        $progressBar->advance();
                        
                    } catch (\Exception $e) {
                        $errorCount++;
                        $this->error("扫描文件失败 {$file->original_name}: " . $e->getMessage());
                        $progressBar->advance();
                    }
                }
            });

        $progressBar->finish();
        $this->newLine(2);

        $this->info("安全扫描完成！");
        $this->info("扫描文件数量: {$scannedCount}");
        $this->info("可疑文件数量: {$suspiciousCount}");
        if ($quarantine) {
            $this->info("隔离文件数量: {$quarantinedCount}");
        }
        $this->info("错误数量: {$errorCount}");

        return $suspiciousCount > 0 ? 1 : 0;
    }

    /**
     * 完整性扫描
     */
    protected function scanIntegrity(int $batchSize): int
    {
        $this->info('正在执行完整性扫描...');
        
        $totalFiles = File::where('is_deleted', false)->count();
        $this->info("总共需要扫描 {$totalFiles} 个文件");

        $scannedCount = 0;
        $missingCount = 0;
        $corruptedCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($totalFiles);
        $progressBar->start();

        File::where('is_deleted', false)
            ->chunk($batchSize, function ($files) use (&$scannedCount, &$missingCount, &$corruptedCount, &$errorCount, $progressBar) {
                foreach ($files as $file) {
                    try {
                        $result = $this->checkFileIntegrity($file);
                        
                        if ($result['missing']) {
                            $missingCount++;
                        } elseif ($result['corrupted']) {
                            $corruptedCount++;
                        }
                        
                        $scannedCount++;
                        $progressBar->advance();
                        
                    } catch (\Exception $e) {
                        $errorCount++;
                        $this->error("检查文件完整性失败 {$file->original_name}: " . $e->getMessage());
                        $progressBar->advance();
                    }
                }
            });

        $progressBar->finish();
        $this->newLine(2);

        $this->info("完整性扫描完成！");
        $this->info("扫描文件数量: {$scannedCount}");
        $this->info("丢失文件数量: {$missingCount}");
        $this->info("损坏文件数量: {$corruptedCount}");
        $this->info("错误数量: {$errorCount}");

        return ($missingCount + $corruptedCount) > 0 ? 1 : 0;
    }

    /**
     * 扫描单个文件的安全性
     */
    protected function scanFileForSecurity(File $file, bool $quarantine): array
    {
        $disk = $file->storage_type === 'local' ? 'citui' : $file->storage_type;
        
        if (!Storage::disk($disk)->exists($file->file_path)) {
            return ['suspicious' => false, 'quarantined' => false, 'reason' => 'file_not_found'];
        }

        $filePath = Storage::disk($disk)->path($file->file_path);
        $suspicious = false;
        $reason = '';

        // 检查文件内容是否包含可疑代码
        $fileContent = file_get_contents($filePath, false, null, 0, 8192); // 读取前8KB
        
        if ($fileContent !== false) {
            $suspiciousPatterns = [
                'eval(',
                'base64_decode(',
                'shell_exec(',
                'system(',
                'exec(',
                'passthru(',
                '<?php',
                '<script',
                'javascript:',
                'DROP TABLE',
                'DELETE FROM',
                'UPDATE SET'
            ];

            foreach ($suspiciousPatterns as $pattern) {
                if (stripos($fileContent, $pattern) !== false) {
                    $suspicious = true;
                    $reason = "包含可疑代码: {$pattern}";
                    break;
                }
            }
        }

        // 检查文件扩展名与内容是否匹配
        if (!$suspicious && $file->isImage()) {
            $imageInfo = @getimagesize($filePath);
            if ($imageInfo === false) {
                $suspicious = true;
                $reason = '图片文件格式无效';
            }
        }

        // 如果发现可疑文件且需要隔离
        $quarantined = false;
        if ($suspicious && $quarantine) {
            $quarantined = $this->quarantineFile($file, $reason);
        }

        // 记录扫描结果
        $this->logScanResult($file, $suspicious, $reason);

        return [
            'suspicious' => $suspicious,
            'quarantined' => $quarantined,
            'reason' => $reason
        ];
    }

    /**
     * 检查文件完整性
     */
    protected function checkFileIntegrity(File $file): array
    {
        $disk = $file->storage_type === 'local' ? 'citui' : $file->storage_type;
        
        // 检查文件是否存在
        if (!Storage::disk($disk)->exists($file->file_path)) {
            $this->warn("文件丢失: {$file->original_name} ({$file->file_path})");
            return ['missing' => true, 'corrupted' => false];
        }

        $filePath = Storage::disk($disk)->path($file->file_path);
        
        // 检查文件大小
        $actualSize = filesize($filePath);
        if ($actualSize !== $file->file_size) {
            $this->warn("文件大小不匹配: {$file->original_name} (期望: {$file->file_size}, 实际: {$actualSize})");
            return ['missing' => false, 'corrupted' => true];
        }

        // 检查文件哈希
        if ($file->file_hash) {
            $actualHash = hash_file('md5', $filePath);
            if ($actualHash !== $file->file_hash) {
                $this->warn("文件哈希不匹配: {$file->original_name}");
                return ['missing' => false, 'corrupted' => true];
            }
        }

        return ['missing' => false, 'corrupted' => false];
    }

    /**
     * 隔离可疑文件
     */
    protected function quarantineFile(File $file, string $reason): bool
    {
        try {
            $disk = $file->storage_type === 'local' ? 'citui' : $file->storage_type;
            $originalPath = $file->file_path;
            
            // 创建隔离目录
            $quarantinePath = 'quarantine/' . date('Y/m/d/') . basename($originalPath);
            
            // 移动文件到隔离区
            if (Storage::disk($disk)->move($originalPath, $quarantinePath)) {
                // 更新数据库记录
                $file->update([
                    'file_path' => $quarantinePath,
                    'is_public' => false, // 设为私有
                    'is_deleted' => true, // 标记为已删除
                    'deleted_at' => now()
                ]);

                // 记录隔离日志
                \App\Models\Citui\OperationLog::create([
                    'user_id' => null,
                    'action' => 'file_quarantined',
                    'data' => json_encode([
                        'file_id' => $file->file_id,
                        'original_path' => $originalPath,
                        'quarantine_path' => $quarantinePath,
                        'reason' => $reason
                    ]),
                    'ip_address' => '127.0.0.1',
                    'user_agent' => 'System Scanner'
                ]);

                $this->warn("文件已隔离: {$file->original_name} - {$reason}");
                return true;
            }
            
        } catch (\Exception $e) {
            $this->error("隔离文件失败 {$file->original_name}: " . $e->getMessage());
        }

        return false;
    }

    /**
     * 记录扫描结果
     */
    protected function logScanResult(File $file, bool $suspicious, string $reason): void
    {
        try {
            \App\Models\Citui\OperationLog::create([
                'user_id' => null,
                'action' => 'file_security_scan',
                'data' => json_encode([
                    'file_id' => $file->file_id,
                    'file_name' => $file->original_name,
                    'suspicious' => $suspicious,
                    'reason' => $reason,
                    'scan_time' => now()->toISOString()
                ]),
                'ip_address' => '127.0.0.1',
                'user_agent' => 'System Scanner'
            ]);
        } catch (\Exception $e) {
            // 记录日志失败不影响扫描过程
        }
    }
}