<?php

use App\Http\Controllers\Api\V1\Wap;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| CitUI API Routes
|--------------------------------------------------------------------------
|
| CitUI应用的API路由定义
| 所有路由都使用 /api/citui 前缀
|
*/

Route::prefix('citui')->group(function () {
    Route::post('user/reg',[Wap\User\UserController::class,'regH5']);
});