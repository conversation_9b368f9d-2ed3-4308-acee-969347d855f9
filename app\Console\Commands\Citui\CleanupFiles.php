<?php
declare(strict_types=1);

namespace App\Console\Commands\Citui;

use Illuminate\Console\Command;
use App\Models\Citui\File;
use App\Services\Citui\FileStorageManager;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class CleanupFiles extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'citui:cleanup-files 
                            {--type=all : 清理类型 (temp|deleted|orphaned|all)}
                            {--force : 强制删除，不询问确认}
                            {--dry-run : 仅显示将要删除的文件，不实际删除}';

    /**
     * 命令描述
     */
    protected $description = '清理CitUI文件系统中的临时文件、软删除文件和孤立文件';

    protected FileStorageManager $storageManager;
    protected array $config;

    public function __construct()
    {
        parent::__construct();
        $this->storageManager = new FileStorageManager();
        $this->config = Config::get('citui_file');
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $force = $this->option('force');
        $dryRun = $this->option('dry-run');

        $this->info('开始清理CitUI文件系统...');
        
        if ($dryRun) {
            $this->warn('运行在预览模式，不会实际删除文件');
        }

        $totalCleaned = 0;
        $totalSize = 0;

        switch ($type) {
            case 'temp':
                [$cleaned, $size] = $this->cleanupTempFiles($force, $dryRun);
                $totalCleaned += $cleaned;
                $totalSize += $size;
                break;

            case 'deleted':
                [$cleaned, $size] = $this->cleanupSoftDeletedFiles($force, $dryRun);
                $totalCleaned += $cleaned;
                $totalSize += $size;
                break;

            case 'orphaned':
                [$cleaned, $size] = $this->cleanupOrphanedFiles($force, $dryRun);
                $totalCleaned += $cleaned;
                $totalSize += $size;
                break;

            case 'all':
            default:
                $this->info('清理临时文件...');
                [$tempCleaned, $tempSize] = $this->cleanupTempFiles($force, $dryRun);
                
                $this->info('清理软删除文件...');
                [$deletedCleaned, $deletedSize] = $this->cleanupSoftDeletedFiles($force, $dryRun);
                
                $this->info('清理孤立文件...');
                [$orphanedCleaned, $orphanedSize] = $this->cleanupOrphanedFiles($force, $dryRun);
                
                $totalCleaned = $tempCleaned + $deletedCleaned + $orphanedCleaned;
                $totalSize = $tempSize + $deletedSize + $orphanedSize;
                break;
        }

        $this->info("清理完成！");
        $this->info("清理文件数量: {$totalCleaned}");
        $this->info("释放空间: " . $this->formatBytes($totalSize));

        return 0;
    }

    /**
     * 清理临时文件
     */
    protected function cleanupTempFiles(bool $force, bool $dryRun): array
    {
        $this->info('正在清理临时文件...');
        
        $tempLifetime = $this->config['cleanup']['temp_file_lifetime'] ?? 24;
        $cutoffTime = Carbon::now()->subHours($tempLifetime);
        
        // 查找过期的临时文件
        $tempFiles = File::where('file_path', 'like', '%/temp/%')
                         ->where('created_at', '<', $cutoffTime)
                         ->where('is_deleted', false)
                         ->get();

        if ($tempFiles->isEmpty()) {
            $this->info('没有找到需要清理的临时文件');
            return [0, 0];
        }

        $this->info("找到 {$tempFiles->count()} 个过期临时文件");

        if (!$force && !$dryRun) {
            if (!$this->confirm('确定要删除这些临时文件吗？')) {
                $this->info('操作已取消');
                return [0, 0];
            }
        }

        $cleaned = 0;
        $totalSize = 0;

        foreach ($tempFiles as $file) {
            try {
                $fileSize = $file->file_size;
                
                if ($dryRun) {
                    $this->line("将删除: {$file->original_name} ({$this->formatBytes($fileSize)})");
                } else {
                    // 删除物理文件
                    $disk = $file->storage_type === 'local' ? 'citui' : $file->storage_type;
                    if (Storage::disk($disk)->exists($file->file_path)) {
                        Storage::disk($disk)->delete($file->file_path);
                    }
                    
                    // 删除数据库记录
                    $file->delete();
                    
                    $this->line("已删除: {$file->original_name} ({$this->formatBytes($fileSize)})");
                }
                
                $cleaned++;
                $totalSize += $fileSize;
                
            } catch (\Exception $e) {
                $this->error("删除文件失败 {$file->original_name}: " . $e->getMessage());
            }
        }

        return [$cleaned, $totalSize];
    }

    /**
     * 清理软删除文件
     */
    protected function cleanupSoftDeletedFiles(bool $force, bool $dryRun): array
    {
        $this->info('正在清理软删除文件...');
        
        $softDeletedLifetime = $this->config['cleanup']['soft_deleted_lifetime'] ?? 30;
        $cutoffTime = Carbon::now()->subDays($softDeletedLifetime);
        
        // 查找过期的软删除文件
        $deletedFiles = File::where('is_deleted', true)
                           ->where('deleted_at', '<', $cutoffTime)
                           ->get();

        if ($deletedFiles->isEmpty()) {
            $this->info('没有找到需要清理的软删除文件');
            return [0, 0];
        }

        $this->info("找到 {$deletedFiles->count()} 个过期软删除文件");

        if (!$force && !$dryRun) {
            if (!$this->confirm('确定要永久删除这些文件吗？此操作不可恢复！')) {
                $this->info('操作已取消');
                return [0, 0];
            }
        }

        $cleaned = 0;
        $totalSize = 0;

        foreach ($deletedFiles as $file) {
            try {
                $fileSize = $file->file_size;
                
                if ($dryRun) {
                    $this->line("将永久删除: {$file->original_name} ({$this->formatBytes($fileSize)})");
                } else {
                    // 删除物理文件
                    $disk = $file->storage_type === 'local' ? 'citui' : $file->storage_type;
                    if (Storage::disk($disk)->exists($file->file_path)) {
                        Storage::disk($disk)->delete($file->file_path);
                    }
                    
                    // 删除文件关联
                    $file->relations()->delete();
                    
                    // 删除数据库记录
                    $file->forceDelete();
                    
                    $this->line("已永久删除: {$file->original_name} ({$this->formatBytes($fileSize)})");
                }
                
                $cleaned++;
                $totalSize += $fileSize;
                
            } catch (\Exception $e) {
                $this->error("删除文件失败 {$file->original_name}: " . $e->getMessage());
            }
        }

        return [$cleaned, $totalSize];
    }

    /**
     * 清理孤立文件
     */
    protected function cleanupOrphanedFiles(bool $force, bool $dryRun): array
    {
        $this->info('正在清理孤立文件...');
        
        $cleaned = 0;
        $totalSize = 0;
        
        // 获取所有存储磁盘
        $disks = ['citui', 'citui_private'];
        
        foreach ($disks as $diskName) {
            try {
                $diskCleaned = $this->cleanupOrphanedFilesOnDisk($diskName, $force, $dryRun);
                $cleaned += $diskCleaned['count'];
                $totalSize += $diskCleaned['size'];
            } catch (\Exception $e) {
                $this->error("清理磁盘 {$diskName} 失败: " . $e->getMessage());
            }
        }

        return [$cleaned, $totalSize];
    }

    /**
     * 清理指定磁盘上的孤立文件
     */
    protected function cleanupOrphanedFilesOnDisk(string $diskName, bool $force, bool $dryRun): array
    {
        $disk = Storage::disk($diskName);
        $cleaned = 0;
        $totalSize = 0;
        
        // 获取磁盘上的所有文件
        $allFiles = $disk->allFiles();
        
        foreach ($allFiles as $filePath) {
            // 跳过缩略图和系统文件
            if (strpos($filePath, '/thumbs/') !== false || 
                strpos($filePath, '.gitignore') !== false ||
                strpos($filePath, '.DS_Store') !== false) {
                continue;
            }
            
            // 检查数据库中是否存在该文件记录
            $fileRecord = File::where('file_path', $filePath)->first();
            
            if (!$fileRecord) {
                try {
                    $fileSize = $disk->size($filePath);
                    
                    if ($dryRun) {
                        $this->line("将删除孤立文件: {$filePath} ({$this->formatBytes($fileSize)})");
                    } else {
                        if (!$force) {
                            if (!$this->confirm("删除孤立文件: {$filePath}?")) {
                                continue;
                            }
                        }
                        
                        $disk->delete($filePath);
                        $this->line("已删除孤立文件: {$filePath} ({$this->formatBytes($fileSize)})");
                    }
                    
                    $cleaned++;
                    $totalSize += $fileSize;
                    
                } catch (\Exception $e) {
                    $this->error("删除孤立文件失败 {$filePath}: " . $e->getMessage());
                }
            }
        }
        
        return ['count' => $cleaned, 'size' => $totalSize];
    }

    /**
     * 格式化字节大小
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return round($bytes, 2) . ' ' . $units[$unitIndex];
    }
}