<?php
namespace App\Service\User;

use Exception;
use App\Utils\Tools;
use App\Service\BaseService;
use App\Exceptions\MyException;
use Illuminate\Support\Facades\DB;
use App\Models\User\User as UserModel;

use Illuminate\Support\Facades\Hash;

class UserService extends BaseService{
    public function loginSms(){
        $data = request()->all();
        $phone = $data['phone'] ?? '';
        $pwd = $data['pwd'] ?? '';
        if(empty($phone) || empty($pwd)){
            throw new MyException('参数错误');
        }
        if(!Tools::isMobile($phone)){
            throw new MyException('手机号格式错误');
        }

        $user = UserModel::where('phone',$phone)->first();
        if(!$user){
            throw new MyException('您的账号不存在');
        }

        if($user->pwd != md5($pwd)){
            throw new MyException('您的账号密码错误');
        }
        //已删除的账号
        if($user->delete_time>0){
            throw new MyException('您的账号不存在');
        }

        if($user->isset==2){
            throw new MyException('您的账号已禁用');
        }

        $user->login_time = time();
        $user->ip = Tools::getIp();

        if(empty($user->invite_code)){
            $user->invite_code = $this->generateInviteCode();
        }

        $user->save();


        $admin_id = $user->admin_id;


        // 处理登录记录

        $login_info = [
            'ip' => Tools::getIp(),
            'ip_address' => '',
            'm_type' => $data['m_type'] ?? 1,
            'm_version' => $data['m_version'] ?? '',
            'm_brand' => $data['m_brand'] ?? '',
            'm_model' => $data['m_model'] ?? '',
            'm_device_id' => $data['m_device_id'] ?? '',
            'm_network' => $data['m_network'] ?? 0,
            'm_netserver' => $data['m_netserver'] ?? 0,
            'm_simulator' => $data['m_simulator'] ?? 0,
        ];


        $user = $user->toArray();
        $pwd = Tools::jsEncrypt($user['phone'].':'.$user['pwd'],$user['pwd']);
        $user['token']=$user['phone'].':'.$pwd;
        return $user;
    }
    /**
     * 生成唯一的6位注册码
     * @return string 生成的注册码
     * @throws MyException 当无法生成唯一注册码时抛出异常
     */

    public function generateInviteCode()
    {
        $maxAttempts = 30; // 最大尝试次数
        $attempts = 0;
        do {
            // 生成6位随机字符串(只包含数字和大写字母,排除易混淆的字符)
            $code = Tools::generateRandomStr(6);
            // 检查是否已存在
            $exists = UserModel::where('invite_code', $code)->exists();
            $attempts++;
            // 如果不存在则返回该码
            if(!$exists) {
                return $code;
            }
        } while($attempts < $maxAttempts);

        // 如果尝试次数过多仍未成功,则抛出异常
        throw new MyException('无法生成唯一邀请码,请重试');
    }

    public function regH5()
    {

        $data = request()->all();

        // 验证必填参数
        if(empty($data['phone']) || empty($data['password']) || empty($data['confirmPassword'])) {
            throw new MyException('请填写完整注册信息');
        }

        // 验证密码是否一致
        if($data['password'] !== $data['confirmPassword']) {
            throw new MyException('两次密码输入不一致');
        }

        // 重新组织参数调用register
        $registerData = [
            'phone' => $data['phone'],
            'pwd' => $data['password'], // 重命名password为pwd
        ];

        // 将参数放入request中
        request()->merge($registerData);

        // 调用原register方法
        return $this->register();
    }

    public function register()
    {
        $data = request()->all();
        $phone = $data['phone'] ?? '';
        $pwd = $data['pwd'] ?? '';

        // 验证必填参数
        if(empty($phone) || empty($pwd)){
            throw new MyException('手机号、密码不能为空');
        }

        // 验证手机号格式
        if(!Tools::isMobileReg($phone)){
            throw new MyException('手机号格式错误');
        }

        // 验证密码长度
        if(strlen($pwd) < 6){
            throw new MyException('密码长度不能小于6位');
        }

        $is_exists = UserModel::where('phone',$phone)->first();
        if($is_exists){
            throw new MyException('该手机号已注册');
        }

        $invite_code = $this->generateInviteCode();

        DB::beginTransaction();
        try {
            // 创建用户
            $userData = [
                'pid' => 0,
                'phone' => $phone,
                'pwd' => md5($pwd),
                'invite_code' => $invite_code,
                'last_login_at' => date('Y-m-d H:i:s'),
                'last_login_ip' => Tools::getIp(),
            ];
            $user = UserModel::create($userData);
            $user->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            // 判断是否是唯一索引冲突异常
            if(str_contains($e->getMessage(), 'Duplicate entry') && str_contains($e->getMessage(), 'phone')) {
                throw new MyException('该手机号已被注册');
            }
            throw new MyException('注册失败');
        }

        $user_info = UserModel::find($user->id)->toArray();

        $pwd = Tools::jsEncrypt($user_info['phone'].':'.$user_info['pwd'],$user_info['pwd']);
        $user_info['token'] = $user_info['phone'].':'.$pwd;
        return $user_info;
    }

}

