<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class ClueFeedbackRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'feedback_type' => 'required|string|in:success,failure,partial,question,suggestion',
            'feedback_content' => 'required|string|max:2000',
            'result_status' => 'required|string|in:success,failure,partial,pending',
            'actual_reward' => 'nullable|numeric|min:0|max:999999.99',
            'execution_time' => 'nullable|integer|min:1|max:10080', // 最多7天（分钟）
            'screenshots' => 'nullable|array|max:5',
            'screenshots.*' => 'image|mimes:jpeg,jpg,png,webp|max:2048',
            'rating' => 'required|integer|min:1|max:5',
            'is_anonymous' => 'nullable|boolean'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'feedback_type' => '反馈类型',
            'feedback_content' => '反馈内容',
            'result_status' => '结果状态',
            'actual_reward' => '实际收益',
            'execution_time' => '执行时间',
            'is_anonymous' => '匿名反馈'
        ]);
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'feedback_type.in' => '反馈类型必须是：成功反馈、失败反馈、部分成功、疑问反馈、建议反馈中的一种',
            'result_status.in' => '结果状态必须是：成功、失败、部分成功、进行中中的一种',
            'execution_time.max' => '执行时间不能超过7天',
            'screenshots.max' => '最多只能上传5张截图',
            'rating.between' => '评分必须在1-5星之间'
        ]);
    }
}