<?php
declare(strict_types=1);

namespace App\Models\Citui;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;

class AdminUser extends Authenticatable
{
    use HasApiTokens;
    
    protected $table = 'admin_users';
    protected $primaryKey = 'admin_id';
    
    protected $fillable = [
        'username',
        'password_hash',
        'real_name',
        'email',
        'phone',
        'avatar_url',
        'role',
        'permissions',
        'is_active'
    ];
    
    protected $hidden = [
        'password_hash'
    ];
    
    protected $casts = [
        'permissions' => 'array',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime'
    ];
    
    /**
     * 获取用于认证的密码字段名
     */
    public function getAuthPassword()
    {
        return $this->password_hash;
    }
    
    /**
     * 设置密码
     */
    public function setPasswordAttribute($value): void
    {
        $this->attributes['password_hash'] = Hash::make($value);
    }
    
    /**
     * 验证密码
     */
    public function checkPassword(string $password): bool
    {
        return Hash::check($password, $this->password_hash);
    }
    
    /**
     * 关联审核记录
     */
    public function audits()
    {
        return $this->hasMany(ContentAudit::class, 'auditor_id', 'admin_id');
    }
    
    /**
     * 关联操作日志
     */
    public function operationLogs()
    {
        // 注意：需要创建OperationLog模型或者使用正确的模型类名
        // 暂时注释掉，避免类不存在的错误
        // return $this->hasMany(OperationLog::class, 'user_id', 'admin_id')
        //             ->where('user_type', 'admin');
        return collect(); // 临时返回空集合
    }
    
    /**
     * 查询作用域 - 活跃管理员
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }
    
    /**
     * 查询作用域 - 按角色筛选
     */
    public function scopeByRole(Builder $query, string $role): Builder
    {
        return $query->where('role', $role);
    }
    
    /**
     * 检查是否有指定权限
     */
    public function hasPermission(string $permission): bool
    {
        if ($this->role === 'super_admin') {
            return true;
        }
        
        return in_array($permission, $this->permissions ?? []);
    }
    
    /**
     * 检查是否有任一权限
     */
    public function hasAnyPermission(array $permissions): bool
    {
        if ($this->role === 'super_admin') {
            return true;
        }
        
        return !empty(array_intersect($permissions, $this->permissions ?? []));
    }
    
    /**
     * 检查是否为超级管理员
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }
    
    /**
     * 检查是否活跃
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }
    
    /**
     * 更新最后登录时间
     */
    public function updateLastLogin(?string $ip = null): void
    {
        $this->last_login_at = now();
        $this->last_login_ip = $ip;
        $this->save();
    }
    
    /**
     * 获取管理员统计数据
     */
    public function getStatistics(): array
    {
        return [
            'audit_count' => $this->audits()->count(),
            'audit_passed' => $this->audits()->where('audit_result', 'pass')->count(),
            'audit_rejected' => $this->audits()->where('audit_result', 'reject')->count(),
            'operation_count' => 0, // 暂时返回0，等OperationLog模型创建后再修复
            'last_login' => $this->last_login_at ? $this->last_login_at->format('Y-m-d H:i:s') : null
        ];
    }
}