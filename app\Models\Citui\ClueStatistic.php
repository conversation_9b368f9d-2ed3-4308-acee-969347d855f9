<?php
declare(strict_types=1);

namespace App\Models\Citui;

use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class ClueStatistic extends BaseCituiModel
{
    protected $table = 'clue_statistics';
    protected $primaryKey = 'stat_id';
    
    protected $fillable = [
        'clue_id',
        'stat_date',
        'view_count',
        'try_count',
        'success_count',
        'failure_count',
        'total_reward',
        'avg_rating',
        'feedback_count'
    ];
    
    protected $casts = [
        'clue_id' => 'integer',
        'stat_date' => 'date',
        'view_count' => 'integer',
        'try_count' => 'integer',
        'success_count' => 'integer',
        'failure_count' => 'integer',
        'total_reward' => 'decimal:2',
        'avg_rating' => 'decimal:2',
        'feedback_count' => 'integer'
    ];
    
    protected $appends = [
        'success_rate',
        'failure_rate'
    ];
    
    /**
     * 关联线索
     */
    public function clue()
    {
        return $this->belongsTo(WaterClue::class, 'clue_id', 'clue_id');
    }
    
    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute(): float
    {
        if ($this->try_count === 0) {
            return 0.0;
        }
        
        return round(($this->success_count / $this->try_count) * 100, 2);
    }
    
    /**
     * 获取失败率
     */
    public function getFailureRateAttribute(): float
    {
        if ($this->try_count === 0) {
            return 0.0;
        }
        
        return round(($this->failure_count / $this->try_count) * 100, 2);
    }
    
    /**
     * 查询作用域 - 按线索筛选
     */
    public function scopeByClue(Builder $query, int $clueId): Builder
    {
        return $query->where('clue_id', $clueId);
    }
    
    /**
     * 查询作用域 - 按日期筛选
     */
    public function scopeByDate(Builder $query, string $date): Builder
    {
        return $query->where('stat_date', $date);
    }
    
    /**
     * 查询作用域 - 日期范围
     */
    public function scopeDateRange(Builder $query, ?string $startDate = null, ?string $endDate = null): Builder
    {
        if ($startDate && $endDate) {
            return $query->whereBetween('stat_date', [$startDate, $endDate]);
        }
        
        // 如果只有开始日期
        if ($startDate) {
            return $query->where('stat_date', '>=', $startDate);
        }
        
        // 如果只有结束日期
        if ($endDate) {
            return $query->where('stat_date', '<=', $endDate);
        }
        
        // 如果都没有，返回原查询
        return $query;
    }
    
    /**
     * 查询作用域 - 今天的统计
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->where('stat_date', Carbon::today());
    }
    
    /**
     * 查询作用域 - 昨天的统计
     */
    public function scopeYesterday(Builder $query): Builder
    {
        return $query->where('stat_date', Carbon::yesterday());
    }
    
    /**
     * 查询作用域 - 本周的统计
     */
    public function scopeThisWeek(Builder $query): Builder
    {
        return $query->whereBetween('stat_date', [
            Carbon::now()->startOfWeek(),
            Carbon::now()->endOfWeek()
        ]);
    }
    
    /**
     * 查询作用域 - 本月的统计
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('stat_date', Carbon::now()->month)
                    ->whereYear('stat_date', Carbon::now()->year);
    }
    
    /**
     * 查询作用域 - 按成功率排序
     */
    public function scopeBySuccessRate(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->selectRaw('*, CASE WHEN try_count > 0 THEN (success_count / try_count * 100) ELSE 0 END as success_rate_calc')
                    ->orderBy('success_rate_calc', $direction);
    }
    
    /**
     * 创建或更新统计记录
     */
    public static function updateOrCreateStat(int $clueId, string $date, array $data): self
    {
        return static::updateOrCreate(
            ['clue_id' => $clueId, 'stat_date' => $date],
            $data
        );
    }
    
    /**
     * 增加查看次数
     */
    public function incrementViewCount(int $count = 1): int
    {
        return $this->increment('view_count', $count);
    }
    
    /**
     * 增加尝试次数
     */
    public function incrementTryCount(int $count = 1): int
    {
        return $this->increment('try_count', $count);
    }
    
    /**
     * 增加成功次数
     */
    public function incrementSuccessCount(int $count = 1): int
    {
        return $this->increment('success_count', $count);
    }
    
    /**
     * 增加失败次数
     */
    public function incrementFailureCount(int $count = 1): int
    {
        return $this->increment('failure_count', $count);
    }
    
    /**
     * 增加总奖励
     */
    public function addTotalReward(float $reward): int
    {
        return $this->increment('total_reward', $reward);
    }
    
    /**
     * 更新平均评分
     */
    public function updateAvgRating(float $newRating): bool
    {
        $currentTotal = $this->avg_rating * $this->feedback_count;
        $newTotal = $currentTotal + $newRating;
        $newCount = $this->feedback_count + 1;
        $newAverage = $newTotal / $newCount;
        
        $this->avg_rating = round($newAverage, 2);
        $this->feedback_count = $newCount;
        
        return $this->save();
    }
    
    /**
     * 获取统计汇总
     */
    public static function getSummaryByClue(int $clueId, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);
        $endDate = Carbon::now();
        
        $stats = static::byClue($clueId)
                      ->dateRange($startDate->format('Y-m-d'), $endDate->format('Y-m-d'))
                      ->get();
        
        return [
            'total_views' => $stats->sum('view_count'),
            'total_tries' => $stats->sum('try_count'),
            'total_successes' => $stats->sum('success_count'),
            'total_failures' => $stats->sum('failure_count'),
            'total_reward' => $stats->sum('total_reward'),
            'avg_rating' => $stats->avg('avg_rating'),
            'total_feedbacks' => $stats->sum('feedback_count'),
            'success_rate' => $stats->sum('try_count') > 0 ? 
                round(($stats->sum('success_count') / $stats->sum('try_count')) * 100, 2) : 0,
            'period_days' => $days
        ];
    }
    
    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'stat_id' => $this->stat_id,
            'clue_id' => $this->clue_id,
            'stat_date' => $this->stat_date->format('Y-m-d'),
            'view_count' => $this->view_count,
            'try_count' => $this->try_count,
            'success_count' => $this->success_count,
            'failure_count' => $this->failure_count,
            'success_rate' => $this->success_rate,
            'failure_rate' => $this->failure_rate,
            'total_reward' => $this->total_reward,
            'avg_rating' => $this->avg_rating,
            'feedback_count' => $this->feedback_count,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s')
        ];
    }
}