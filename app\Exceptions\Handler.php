<?php
declare(strict_types=1);
namespace App\Exceptions;

use App\Helpers\Traits\ApiResponse;
use ErrorException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Throwable;
use Illuminate\Database\Eloquent\ModelNotFoundException;

use Exception;


class Handler extends ExceptionHandler
{
    use ApiResponse;
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     */
    public function report(Exception|Throwable $exception)
    {
        parent::report($exception);
    }


    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param $request
     * @param Exception $exception
     * @return Response
     *
     * @throws Exception
     */
    public function render($request, Exception|Throwable $exception)
    {

        $file = '  '.str_ireplace([base_path(),'\\'],['','/'],$exception->getFile()).'  ';

        if(config('app.debug')){
            //调试模式
            systemLog($exception->getMessage().$file.$exception->getLine());
        }
        if ($exception instanceof MyException || $exception instanceof ValidationException
        ){
            //return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_UNPROCESSABLE_ENTITY);
            $reg_1 = "/ \(and.*more error[s]*\)/";
            $message = preg_replace($reg_1,'',$exception->getMessage());
            if($exception->getCode()){
                return $this->apiFailed($message,200,$exception->getCode());
            }
            return $this->apiFailed($message);
        }elseif($exception instanceof ModelNotFoundException){
            $message = '信息不存在';
            return $this->apiFailed($message);
        }elseif($exception instanceof ErrorException){
            $reg_1 = "/ \(and.*more error[s]*\)/";
            $message = preg_replace($reg_1,'',$exception->getMessage());
            $message = str_ireplace(['"'],'',$message);
            if($exception->getCode()){
                return $this->apiFailed($message,200,$exception->getCode());
            }
            return $this->apiFailed($message);
        }else{
            return $this->apiFailed("系统错误");
        }

        return parent::render($request, $exception);
    }

}
