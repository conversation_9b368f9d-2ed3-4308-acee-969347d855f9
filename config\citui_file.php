<?php

return [
    /*
    |--------------------------------------------------------------------------
    | CitUI 文件管理配置
    |--------------------------------------------------------------------------
    |
    | CitUI 应用的文件管理相关配置
    |
    */

    // 默认存储驱动
    'default_disk' => env('CITUI_FILE_DISK', 'citui'),

    // 私有文件存储驱动
    'private_disk' => env('CITUI_PRIVATE_FILE_DISK', 'citui_private'),

    // 文件上传限制
    'upload_limits' => [
        // 单个文件最大大小 (字节)
        'max_file_size' => env('CITUI_MAX_FILE_SIZE', 10 * 1024 * 1024), // 10MB
        
        // 批量上传最大文件数量
        'max_batch_files' => env('CITUI_MAX_BATCH_FILES', 10),
        
        // 批量上传总大小限制 (字节)
        'max_batch_size' => env('CITUI_MAX_BATCH_SIZE', 50 * 1024 * 1024), // 50MB
        
        // 用户每日上传限制 (字节)
        'daily_upload_limit' => env('CITUI_DAILY_UPLOAD_LIMIT', 100 * 1024 * 1024), // 100MB
    ],

    // 支持的文件类型
    'allowed_types' => [
        'image' => [
            'extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'mime_types' => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'max_size' => 5 * 1024 * 1024, // 5MB
        ],
        'video' => [
            'extensions' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
            'mime_types' => ['video/mp4', 'video/avi', 'video/quicktime', 'video/x-ms-wmv'],
            'max_size' => 50 * 1024 * 1024, // 50MB
        ],
        'document' => [
            'extensions' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
            'mime_types' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain'
            ],
            'max_size' => 10 * 1024 * 1024, // 10MB
        ],
        'audio' => [
            'extensions' => ['mp3', 'wav', 'aac', 'flac', 'ogg'],
            'mime_types' => ['audio/mpeg', 'audio/wav', 'audio/aac', 'audio/flac', 'audio/ogg'],
            'max_size' => 20 * 1024 * 1024, // 20MB
        ],
        'archive' => [
            'extensions' => ['zip', 'rar', '7z', 'tar', 'gz'],
            'mime_types' => [
                'application/zip',
                'application/x-rar-compressed',
                'application/x-7z-compressed',
                'application/x-tar',
                'application/gzip'
            ],
            'max_size' => 20 * 1024 * 1024, // 20MB
        ],
    ],

    // 文件存储路径配置
    'storage_paths' => [
        'avatar' => 'avatars/{year}/{month}',
        'evaluation' => 'evaluations/{year}/{month}',
        'clue' => 'clues/{year}/{month}',
        'document' => 'documents/{year}/{month}',
        'temp' => 'temp/{year}/{month}/{day}',
        'other' => 'files/{year}/{month}',
    ],

    // 图片处理配置
    'image_processing' => [
        // 是否启用图片压缩
        'enable_compression' => env('CITUI_IMAGE_COMPRESSION', true),
        
        // 图片质量 (1-100)
        'quality' => env('CITUI_IMAGE_QUALITY', 85),
        
        // 最大宽度
        'max_width' => env('CITUI_IMAGE_MAX_WIDTH', 1920),
        
        // 最大高度
        'max_height' => env('CITUI_IMAGE_MAX_HEIGHT', 1080),
        
        // 缩略图尺寸
        'thumbnail_sizes' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600],
        ],
    ],

    // 文件清理配置
    'cleanup' => [
        // 临时文件保留时间 (小时)
        'temp_file_lifetime' => env('CITUI_TEMP_FILE_LIFETIME', 24),
        
        // 软删除文件保留时间 (天)
        'soft_deleted_lifetime' => env('CITUI_SOFT_DELETED_LIFETIME', 30),
        
        // 是否启用自动清理
        'auto_cleanup' => env('CITUI_AUTO_CLEANUP', true),
    ],

    // 安全配置
    'security' => [
        // 是否启用文件内容检测
        'enable_content_scan' => env('CITUI_CONTENT_SCAN', true),
        
        // 禁止的文件扩展名
        'forbidden_extensions' => [
            'php', 'php3', 'php4', 'php5', 'phtml', 'pht',
            'jsp', 'asp', 'aspx', 'js', 'html', 'htm',
            'exe', 'bat', 'cmd', 'com', 'scr', 'vbs',
            'sh', 'bash', 'csh', 'ksh', 'fish'
        ],
        
        // 危险的MIME类型
        'forbidden_mime_types' => [
            'application/x-php',
            'application/x-httpd-php',
            'application/x-httpd-php-source',
            'application/x-executable',
            'application/x-msdownload',
            'text/x-php',
            'text/html'
        ],
        
        // 文件名黑名单模式
        'forbidden_filename_patterns' => [
            '/\.php$/i',
            '/\.jsp$/i',
            '/\.asp$/i',
            '/\.exe$/i',
            '/\.bat$/i',
            '/\.cmd$/i',
        ],
    ],

    // CDN配置 (可选)
    'cdn' => [
        // 是否启用CDN
        'enabled' => env('CITUI_CDN_ENABLED', false),
        
        // CDN域名
        'domain' => env('CITUI_CDN_DOMAIN', ''),
        
        // CDN路径前缀
        'path_prefix' => env('CITUI_CDN_PATH_PREFIX', ''),
    ],

    // 水印配置 (可选)
    'watermark' => [
        // 是否启用水印
        'enabled' => env('CITUI_WATERMARK_ENABLED', false),
        
        // 水印图片路径
        'image_path' => env('CITUI_WATERMARK_IMAGE', ''),
        
        // 水印位置 (top-left, top-right, bottom-left, bottom-right, center)
        'position' => env('CITUI_WATERMARK_POSITION', 'bottom-right'),
        
        // 水印透明度 (0-100)
        'opacity' => env('CITUI_WATERMARK_OPACITY', 50),
        
        // 水印边距
        'margin' => env('CITUI_WATERMARK_MARGIN', 10),
    ],
];