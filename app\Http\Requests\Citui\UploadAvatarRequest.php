<?php
declare(strict_types=1);

namespace App\Http\Requests\Citui;

class UploadAvatarRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'avatar' => [
                'required',
                'image',
                'mimes:jpeg,jpg,png,webp',
                'max:2048', // 2MB
                'dimensions:min_width=100,min_height=100,max_width=2000,max_height=2000'
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return array_merge(parent::messages(), [
            'avatar.required' => '请选择要上传的头像文件',
            'avatar.image' => '头像必须是图片文件',
            'avatar.mimes' => '头像只支持 JPEG、PNG、WebP 格式',
            'avatar.max' => '头像文件大小不能超过 2MB',
            'avatar.dimensions' => '头像尺寸必须在 100x100 到 2000x2000 像素之间'
        ]);
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return array_merge(parent::attributes(), [
            'avatar' => '头像'
        ]);
    }
}