<?php
namespace App\Http\Controllers\Api\V1\Citui\User;
use App\Http\Controllers\Api\Controller;
use App\Service\User\UserService;

class UserController extends Controller
{
    protected $userService;
    public function __construct(UserService $userService){
        parent::__construct();
        $this->userService = $userService;
    }

    public function reg(){
        return $this->apiSuccess($this->userService->regH5());
    }

}